<script lang="ts">
	import type { PageData } from './$types';
	import * as Table from '$lib/components/ui/table';
	import { Button } from '$lib/components/ui/button';

	let { data }: { data: PageData } = $props();
	let volunteerScans = data.volunteerScans || [];

	// Format timestamp to dd MMM yyyy hh:mm:ss AM/PM
	function formatTimestamp(timestamp: string): string {
		const date = new Date(timestamp);

		// Format date part: dd MMM yyyy
		const day = date.getDate().toString().padStart(2, '0');
		const month = date.toLocaleString('en-US', { month: 'short' });
		const year = date.getFullYear();

		// Format time part: hh:mm:ss AM/PM
		const time = date.toLocaleString('en-US', {
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			hour12: true
		});

		return `${day} ${month} ${year} ${time}`;
	}

	function exportToCSV() {
		if (volunteerScans.length === 0) return;

		// Create CSV header
		const headers = Object.keys(volunteerScans[0]).join(',');

		// Create CSV rows
		const csvRows = volunteerScans.map((scan) =>
			Object.values(scan)
				.map((value) => (typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value))
				.join(',')
		);

		// Combine header and rows
		const csvContent = [headers, ...csvRows].join('\n');

		// Create download link
		const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
		const url = URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.setAttribute('href', url);
		link.setAttribute('download', `volunteer-scans-${new Date().toISOString().split('T')[0]}.csv`);
		link.style.visibility = 'hidden';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}
</script>

<div class="container mx-auto py-8">
	<div class="flex justify-between items-center mb-6">
		<h1 class="text-2xl font-bold">Organiser Scan Reports</h1>
		<Button on:click={exportToCSV}>Export to CSV</Button>
	</div>

	<div class="rounded-lg border">
		<!-- <h2 class="text-xl font-semibold p-4">Organiser Scans</h2> -->
		<Table.Root>
			<Table.Header class="sticky top-0 bg-white z-10">
				<Table.Row>
					<Table.Head>ID</Table.Head>
					<Table.Head>Timestamp</Table.Head>
					<Table.Head>Badge ID</Table.Head>
					<Table.Head>QR Data</Table.Head>
					<Table.Head>First Name</Table.Head>
					<Table.Head>Last Name</Table.Head>
					<Table.Head>Scan Type</Table.Head>
					<Table.Head>Scanner</Table.Head>
					<Table.Head>Gate</Table.Head>
				</Table.Row>
			</Table.Header>
			<Table.Body>
				{#each volunteerScans as scan, index}
					<Table.Row>
						<Table.Cell class="border-r bg-muted">{index + 1}</Table.Cell>
						<!-- <Table.Cell>{new Date(scan.timestamp).toLocaleString()}</Table.Cell> -->
						<Table.Cell>{formatTimestamp(scan.timestamp)}</Table.Cell>
						<Table.Cell>{scan.badge_id || 'N/A'}</Table.Cell>
						<Table.Cell>{scan.qr_data}</Table.Cell>
						<Table.Cell>{scan.first_name || 'N/A'}</Table.Cell>
						<Table.Cell>{scan.last_name || 'N/A'}</Table.Cell>
						<Table.Cell>{scan.scan_type}</Table.Cell>
						<Table.Cell>{scan.scanner_name}</Table.Cell>
						<Table.Cell>{scan.gate}</Table.Cell>
					</Table.Row>
				{:else}
					<Table.Row>
						<Table.Cell colspan="10" class="text-center py-4">No Organiser scans found</Table.Cell>
					</Table.Row>
				{/each}
			</Table.Body>
		</Table.Root>
	</div>
</div>
