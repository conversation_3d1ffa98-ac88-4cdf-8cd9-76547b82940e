export function combineFunctionalAreas(row) {
    const mapping = {
        fa_acc: "ACC",
        fa_com: "COM",
        fa_crl: "CRL",
        fa_eva: "EVA",
        fa_fnp: "FNP",
        fa_fbs: "FBS",
        fa_gmh: "GMH",
        fa_log: "LOG",
        fa_mme: "MME",
        fa_ogc: "OGC",
        fa_prg: "PRG",
        fa_tml: "TML",
        fa_wkf: "WKF"
    };

    const result = {};
    for (const key in mapping) {
        result[mapping[key]] = row[key] ?? false;
    }

    return result;
}