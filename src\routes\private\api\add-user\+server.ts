import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { generateQR } from '$lib/server/generateqr';
import { randomBytes } from 'crypto';

// Helper function to capitalize names properly
function capitalizeName(name: string): string {
	if (!name) return '';
	return name.trim()
		.split(' ')
		.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
		.join(' ');
}

// Helper function to generate unique QR data
function generateQRData(prefix: string = 'USR'): string {
	const timestamp = Date.now().toString(36); // Base36 timestamp
	const randomPart = randomBytes(4).toString('hex').toUpperCase(); // 8 character hex
	return `${prefix}${timestamp}${randomPart}`;
}

// Helper function to check if QR data exists in database
async function qrDataExists(qrData: string, supabase: any): Promise<boolean> {
	const { data, error } = await supabase
		.from('workforce')
		.select('id')
		.eq('qr_data', qrData)
		.single();

	if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
		console.error('Error checking QR data existence:', error);
		return false;
	}

	return !!data;
}

// Helper function to generate unique QR data
async function generateUniqueQRData(supabase: any, prefix: string = 'USR', maxAttempts: number = 10): Promise<string> {
	for (let attempt = 1; attempt <= maxAttempts; attempt++) {
		const qrData = generateQRData(prefix);
		const exists = await qrDataExists(qrData, supabase);

		if (!exists) {
			return qrData;
		}

		console.log(`QR data collision detected (attempt ${attempt}): ${qrData}`);
	}

	// If we still haven't found a unique one, add more randomness
	const timestamp = Date.now().toString(36);
	const randomPart = randomBytes(8).toString('hex').toUpperCase(); // 16 character hex
	const fallbackQR = `${prefix}${timestamp}${randomPart}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

	console.warn(`Using fallback QR data after ${maxAttempts} attempts: ${fallbackQR}`);
	return fallbackQR;
}

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
	try {
		const data = await request.json();

		console.log('User registration data received:', data);

		// Validate required fields
		if (!data.firstName?.trim()) {
			return json({ error: 'First name is required' }, { status: 400 });
		}

		if (!data.email?.trim()) {
			return json({ error: 'Email is required' }, { status: 400 });
		}

		if (!data.mobile?.trim()) {
			return json({ error: 'Mobile number is required' }, { status: 400 });
		}

		if (!data.category?.trim()) {
			return json({ error: 'Category is required' }, { status: 400 });
		}

		if (!data.tshirtSize?.trim()) {
			return json({ error: 'T-shirt size is required' }, { status: 400 });
		}

		// Validate email format
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(data.email)) {
			return json({ error: 'Please enter a valid email address' }, { status: 400 });
		}

		// Check if email already exists
		const { data: existingUser, error: checkError } = await supabase
			.from('workforce')
			.select('id')
			.eq('email', data.email.toLowerCase())
			.single();

		if (checkError && checkError.code !== 'PGRST116') {
			console.error('Error checking existing email:', checkError);
			return json({ error: 'Database error occurred' }, { status: 500 });
		}

		if (existingUser) {
			return json({ error: 'A user with this email already exists' }, { status: 400 });
		}

		// Prepare data with proper capitalization
		const capitalizedFirstName = capitalizeName(data.firstName);
		const capitalizedLastName = data.lastName ? capitalizeName(data.lastName) : null;
		const capitalizedCategory = data.category.toUpperCase();
		const capitalizedTshirtSize = data.tshirtSize.toUpperCase();

		// Generate unique QR data
		const uniqueQRData = await generateUniqueQRData(supabase, 'USR');
		console.log(`Generated unique QR data: ${uniqueQRData}`);

		// Generate QR code and get public URL
		let qrCodeUrl = null;
		try {
			const qrResult = await generateQR(uniqueQRData, `user-${Date.now()}`);
			qrCodeUrl = qrResult.url;
			console.log(`Generated QR code URL: ${qrCodeUrl}`);
		} catch (qrError) {
			console.error('Error generating QR code:', qrError);
			// Continue with insertion even if QR generation fails
		}

		// Insert user into database
		const { data: insertedUser, error: insertError } = await supabase
			.from('workforce')
			.insert({
				first_name: capitalizedFirstName,
				last_name: capitalizedLastName,
				email: data.email.toLowerCase(),
				mobile: data.mobile.trim(),
				whatsapp: data.whatsapp?.trim() || null,
				organization: data.organization?.trim() || null,
				tshirt_size: capitalizedTshirtSize,
				category: capitalizedCategory,
				qr_data: uniqueQRData,
				qr_code_url: qrCodeUrl,
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString()
			})
			.select()
			.single();

		if (insertError) {
			console.error('Error inserting user:', insertError);
			return json({ error: 'Failed to add user to database' }, { status: 500 });
		}

		console.log('User added successfully:', insertedUser);

		return json({ 
			message: 'User added successfully',
			user: {
				id: insertedUser.id,
				firstName: insertedUser.first_name,
				lastName: insertedUser.last_name,
				email: insertedUser.email,
				qrData: insertedUser.qr_data
			}
		}, { status: 201 });

	} catch (error) {
		console.error('Error in add-user API:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
