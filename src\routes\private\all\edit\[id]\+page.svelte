<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { PageData } from './$types';
	import { goto } from '$app/navigation';

	let { data }: { data: PageData } = $props();
	let guest = data.guest;

	let formData = {
		firstName: guest.first_name || '',
		lastName: guest.last_name || '',
		email: guest.email || '',
		mobile: guest.mobile || '',
		whatsapp: guest.whatsapp || '',
		organization: guest.organization || '',
		tshirtSize: guest.tshirt_size || '',
		category: guest.category || ''
	};

	async function handleSubmit() {
		try {
			const response = await fetch(`/private/api/edit-data/${guest.id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(formData)
			});

			if (!response.ok) {
				throw new Error('Failed to update data');
			}

			alert('Guest details updated successfully!');
			goto('/private/all');
		} catch (error) {
			console.error('Error updating data:', error);
			alert('Failed to update data');
		}
	}
	async function deleteGuest() {
		const guestName = formData.lastName
			? `${formData.firstName} ${formData.lastName}`
			: formData.firstName;
		if (!confirm(`Are you sure you want to delete ${guestName}?`)) return;

		try {
			const response = await fetch(`/private/api/delete-guest/${guest.id}`, {
				method: 'DELETE'
			});

			if (!response.ok) {
				throw new Error('Failed to delete guest');
			}

			alert('Guest deleted successfully!');
			goto('/private/all');
		} catch (error) {
			console.error('Error deleting guest:', error);
			alert('Failed to delete guest. Please check the console for details.');
		}
	}
</script>

<div class="container mx-auto p-4 max-w-2xl">
	<h1 class="text-2xl font-bold mb-6">Edit Guest Details</h1>

	<form on:submit|preventDefault={handleSubmit} class="space-y-6">
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			<!-- QR Data (Read-only) -->
			<div class="space-y-2 md:col-span-2">
				<label for="qrData" class="block font-medium text-gray-700">QR Data (Read-only)</label>
				<input
					id="qrData"
					type="text"
					value={guest.qr_data}
					class="w-full p-2 border rounded bg-gray-100 text-gray-600"
					readonly
				/>
			</div>

			<!-- First Name -->
			<div class="space-y-2">
				<label for="firstName" class="block font-medium text-gray-700">First Name *</label>
				<input
					id="firstName"
					type="text"
					bind:value={formData.firstName}
					class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
					required
				/>
			</div>

			<!-- Last Name -->
			<div class="space-y-2">
				<label for="lastName" class="block font-medium text-gray-700">Last Name</label>
				<input
					id="lastName"
					type="text"
					bind:value={formData.lastName}
					class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
				/>
			</div>

			<!-- Email -->
			<div class="space-y-2">
				<label for="email" class="block font-medium text-gray-700">Email</label>
				<input
					id="email"
					type="email"
					bind:value={formData.email}
					class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
				/>
			</div>

			<!-- Category -->
			<div class="space-y-2">
				<label for="category" class="block font-medium text-gray-700">Category</label>
				<select
					id="category"
					bind:value={formData.category}
					class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
				>
					<option value="">Select Category</option>
					<option value="YOGA TRAINER">YOGA TRAINER</option>
					<option value="ACTIVATION">ACTIVATION</option>
					<option value="EMBASSY OFFICIAL">EMBASSY OFFICIAL</option>
					<option value="VOLUNTEER">VOLUNTEER</option>
					<option value="ORGANISER">ORGANISER</option>
					<option value="MEDIA">MEDIA</option>
					<option value="MEDICAL">MEDICAL</option>
					<option value="GUEST">GUEST</option>
					<option value="PARTICIPANT">PARTICIPANT</option>
				</select>
			</div>

			<!-- Mobile -->
			<div class="space-y-2">
				<label for="mobile" class="block font-medium text-gray-700">Mobile</label>
				<input
					id="mobile"
					type="tel"
					bind:value={formData.mobile}
					class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
				/>
			</div>

			<!-- WhatsApp -->
			<div class="space-y-2">
				<label for="whatsapp" class="block font-medium text-gray-700">WhatsApp</label>
				<input
					id="whatsapp"
					type="tel"
					bind:value={formData.whatsapp}
					class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
				/>
			</div>

			<!-- Organization -->
			<div class="space-y-2">
				<label for="organization" class="block font-medium text-gray-700">Organization</label>
				<input
					id="organization"
					type="text"
					bind:value={formData.organization}
					class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
				/>
			</div>

			<!-- T-shirt Size -->
			<div class="space-y-2">
				<label for="tshirtSize" class="block font-medium text-gray-700">T-shirt Size</label>
				<select
					id="tshirtSize"
					bind:value={formData.tshirtSize}
					class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
				>
					<option value="">Select Size</option>
					<option value="XS">XS</option>
					<option value="S">S</option>
					<option value="M">M</option>
					<option value="L">L</option>
					<option value="XL">XL</option>
					<option value="XXL">XXL</option>
					<option value="XXXL">XXXL</option>
				</select>
			</div>
		</div>

		<div class="flex justify-between items-center pt-6">
			<div class="space-x-4">
				<Button type="submit" class="bg-blue-600 hover:bg-blue-700">Save Changes</Button>
				<Button type="button" variant="outline" onclick={() => goto('/private/all')}>Cancel</Button>
			</div>

			<div>
				<Button type="button" variant="destructive" onclick={deleteGuest}>Delete Guest</Button>
			</div>
		</div>
	</form>
</div>
