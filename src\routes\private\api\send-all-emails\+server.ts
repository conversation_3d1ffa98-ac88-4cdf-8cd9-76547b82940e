import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { sendEmailToAllGuests } from '$lib/server/sendEmail';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const { guests, eventDetails } = await request.json();

        // Validate input
        if (!guests || !Array.isArray(guests) || !eventDetails) {
            return json({ success: false, error: 'Missing guests array or event details' }, { status: 400 });
        }

        if (guests.length === 0) {
            return json({ success: false, error: 'No guests provided' }, { status: 400 });
        }

        console.log(`Starting bulk email send to ${guests.length} guests`);

        // Send emails using the server function
        const result = await sendEmailToAllGuests(guests, eventDetails);

        // Update email status in database for successful sends
        if (result.successful > 0) {
            try {
                const successfulEmails = result.results
                    .filter(r => r.success)
                    .map(r => r.guestEmail);

                if (successfulEmails.length > 0) {
                    // Find guest IDs for successful emails
                    const guestIds = guests
                        .filter(guest => successfulEmails.includes(guest.email))
                        .map(guest => guest.id);

                    const { error: updateError } = await supabase
                        .from('workforce')
                        .update({
                            email_status: 'sent',
                            email_sent_at: new Date().toISOString()
                        })
                        .in('id', guestIds);

                    if (updateError) {
                        console.error('Error updating email statuses:', updateError);
                        // Don't fail the request if status update fails
                    } else {
                        console.log(`Updated email status for ${guestIds.length} guests`);
                    }
                }
            } catch (dbError) {
                console.error('Database update error:', dbError);
                // Don't fail the request if status update fails
            }
        }

        // Update failed email status
        if (result.failed > 0) {
            try {
                const failedEmails = result.results
                    .filter(r => !r.success)
                    .map(r => r.guestEmail);

                if (failedEmails.length > 0) {
                    // Find guest IDs for failed emails
                    const failedGuestIds = guests
                        .filter(guest => failedEmails.includes(guest.email))
                        .map(guest => guest.id);

                    const { error: updateError } = await supabase
                        .from('workforce')
                        .update({
                            email_status: 'failed',
                            email_sent_at: new Date().toISOString()
                        })
                        .in('id', failedGuestIds);

                    if (updateError) {
                        console.error('Error updating failed email statuses:', updateError);
                    } else {
                        console.log(`Updated failed email status for ${failedGuestIds.length} guests`);
                    }
                }
            } catch (dbError) {
                console.error('Database update error for failed emails:', dbError);
            }
        }

        return json({ 
            success: true, 
            message: `Email sending completed. ${result.successful} successful, ${result.failed} failed.`,
            data: result 
        });

    } catch (error) {
        console.error('Error in send-all-emails API:', error);
        return json({ 
            success: false, 
            error: 'Internal server error' 
        }, { status: 500 });
    }
};
