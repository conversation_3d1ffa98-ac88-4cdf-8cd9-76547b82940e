<script>
	import { But<PERSON> } from '$lib/components/ui/button';
</script>

<div class="min-h-screen flex flex-col items-center justify-center p-4">
	<h1 class="text-4xl font-bold mb-8">Yoga 2025 Back Office</h1>
	<Button href="/private/csv">Read CSV and Save to DB</Button><br />
	<Button
		href="/private/add-user"
		class="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors mb-4"
	>
		Add New User
	</Button>
	<!-- <Button href="/private/manual-badge-generator">Manual Badge Generation</Button><br /> -->
	<h2 class="font-bold text-3xl mb-1">Data</h2>
	<!-- <p class="text-sm mb-6">Reads the Database for the Category and generates the badges</p> -->
	<div class="grid">
		<!-- <div class="grid"> -->
		<!-- <Button
			href="/private/guest"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>GUEST
		</Button>

		<Button
			href="/private/leader"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>TEAM LEADER
		</Button>

		<Button
			href="/private/media"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>MEDIA
		</Button>

		<Button
			href="/private/medical"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>MEDICAL
		</Button>

		<Button
			href="/private/organiser"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>ORGANISER
		</Button>

		<Button
			href="/private/player"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>PLAYER
		</Button>

		<Button
			href="/private/security"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>SECURITY
		</Button> -->

		<!-- <Button
			href="/private/services"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>SERVICES
		</Button>

		<Button
			href="/private/team"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>TEAM OFFICIAL
		</Button>

		<Button
			href="/private/volunteer"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>VOLUNTEER
		</Button> -->

		<Button
			href="/private/all"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>All Data
		</Button>

		<!-- <Button
			href="/private/recent"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>Recently Added
		</Button> -->
	</div>
	<h2 class="font-bold text-3xl mt-4">Scanner Setup</h2>
	<div class="mt-4">
		<Button
			href="/private/scanner-config"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>Scanner Config
		</Button>
		<Button
			href="/private/scanner-logs"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>Scanner Logs
		</Button>
		<Button
			href="/scanner"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>Start Scanner
		</Button>
	</div>

	<!-- <h2 class="font-bold text-3xl mt-4">Reports</h2>
	<div class="mt-4">
		<Button
			href="/private/volunteer-reports"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>Volunteer Reports
		</Button>
		<Button
			href="/private/organiser-reports"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>Organiser Reports
		</Button>
	</div> -->
	<!-- <h2 class="font-bold text-3xl mt-4">Lucky Draw</h2>
	<div class="mt-4">
		<Button
			href="/private/lucky-draw"
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			>Lucky Draw
		</Button>
	</div> -->
</div>

<style lang="postcss">
	:global(html) {
		background-color: theme(colors.gray.100);
	}
</style>
