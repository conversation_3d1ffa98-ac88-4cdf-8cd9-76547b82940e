import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import bcrypt from 'bcrypt';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const { username, password } = await request.json();

        // Fetch scanner with matching username
        const { data: scanner, error } = await supabase
            .from('scanners')
            .select('*')
            .eq('username', username)
            .single();

        if (error || !scanner) {
            console.error('Scanner login error:', error || 'Scanner not found');
            return json({ success: false, error: 'Invalid credentials' }, { status: 401 });
        }

        // Verify password
        // const passwordMatch = await bcrypt.compare(password, scanner.password_hash);
        const passwordMatch = password === scanner.password_hash;


        if (!passwordMatch) {
            return json({ success: false, error: 'Invalid credentials' }, { status: 401 });
        }

        // Return scanner info (excluding password hash)
        const { password_hash, ...scannerInfo } = scanner;

        return json({
            success: true,
            scanner: scannerInfo
        });
    } catch (error) {
        console.error('Error in scanner login endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};