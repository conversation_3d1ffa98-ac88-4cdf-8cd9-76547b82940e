<script>
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import QrcodeStream from '$lib/components/qrcode-stream.svelte';
	import { BarcodeDetector } from 'barcode-detector';
	import { fetchImageBuffer } from '$lib/utils/fetchImageBuffer';

	let isLoggedIn = false;
	let isScanning = false;
	let scannerConfig = null;
	let username = '';
	let password = '';
	let scanResult = null;
	let offlineScans = [];
	let showSettings = false;
	let decodedText = null;
	let showForceOptions = false;
	// #TODO: Add option to enter qrdata and submit
	// Load offline scans from localStorage
	onMount(() => {
		if (browser) {
			// Check if BarcodeDetector is supported
			if ('BarcodeDetector' in window) {
				console.log('BarcodeDetector is supported!');
				// Check supported formats
				BarcodeDetector.getSupportedFormats()
					.then((formats) => console.log('Supported formats:', formats))
					.catch((err) => console.error('Error getting supported formats:', err));
			} else {
				console.warn('BarcodeDetector is NOT supported in this browser');
				alert('Your browser may not support barcode detection. Please try a different browser.');
			}

			// Rest of your existing onMount code...
			const storedScans = localStorage.getItem('offlineScans');
			if (storedScans) {
				offlineScans = JSON.parse(storedScans);
			}

			// Check if already logged in
			const storedConfig = localStorage.getItem('scannerConfig');
			if (storedConfig) {
				scannerConfig = JSON.parse(storedConfig);
				isLoggedIn = true;
			}
		}

		console.log('Scan Data: ');
	});

	async function login() {
		try {
			const response = await fetch('/api/scanner/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ username, password })
			});

			if (!response.ok) {
				throw new Error('Login failed');
			}

			const data = await response.json();
			scannerConfig = data.scanner;

			// Store scanner config in localStorage
			localStorage.setItem('scannerConfig', JSON.stringify(scannerConfig));
			isLoggedIn = true;
		} catch (error) {
			console.error('Login error:', error);
			alert('Login failed. Please check your credentials.');
		}
	}

	function logout() {
		if (confirm('Are you sure you want to log out?')) {
			isLoggedIn = false;
			scannerConfig = null;
			localStorage.removeItem('scannerConfig');
			stopScanner();
		}
	}

	function startScanner() {
		console.log('Starting scanner...');
		isScanning = true;
	}

	function stopScanner() {
		isScanning = false;
	}

	function onDetect(detectedCodes) {
		console.log('onDetect called with:', detectedCodes);
		if (detectedCodes && detectedCodes.length > 0) {
			// Process the first detected barcode
			console.log('QR code detected:', detectedCodes[0].rawValue);
			// alert('Barcode detected: ' + detectedCodes[0].rawValue);
			decodedText = detectedCodes[0].rawValue;
			onScanSuccess(decodedText);
			stopScanner();
		}
	}

	function onScanError(error) {
		console.error('Scanning error:', error);
		alert(`Failed to start scanner: ${error.message}`);
	}

	async function onScanSuccess(decodedText) {
		// Stop scanner after successful scan
		stopScanner();

		// Process the scan
		const timestamp = new Date().toISOString();
		const scanType = getScanType();

		// Create scan record
		const scanRecord = {
			qr_data: decodedText,
			timestamp,
			scanner_id: scannerConfig.id,
			scanner_name: scannerConfig.name,
			gate: scannerConfig.gate,
			scan_type: scanType,
			processed: false,
			status: 'pending' // Add status field
		};

		// Try to send to server
		try {
			// First, check for previous scans of this QR code
			const prevScanResponse = await fetch(
				`/api/scanner/previous-scans?qr_data=${encodeURIComponent(decodedText)}`,
				{
					method: 'GET',
					headers: {
						'Content-Type': 'application/json'
					}
				}
			);

			const prevScanResult = await prevScanResponse.json();

			// Now record the current scan
			const response = await fetch('/api/scanner/record-scan', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(scanRecord)
			});

			const result = await response.json();

			if (!response.ok) {
				// Handle validation errors but still record the scan
				if (result.validation_failed) {
					scanRecord.status = 'invalid'; // Mark as invalid
					console.log('invalid QR Call API getting Called.');
					// Store the invalid scan
					try {
						await fetch('/api/scanner/record-invalid-scan', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json'
							},
							body: JSON.stringify(scanRecord)
						});
					} catch (invalidScanError) {
						console.error('Error recording invalid scan:', invalidScanError);
					}

					scanResult = {
						success: false,
						message: result.error || 'Access validation failed',
						data: result.user_data || null,
						previousScans: prevScanResult.data || [],
						validation_failed: true
					};
					return;
				}
				throw new Error('Failed to record scan');
			}

			scanResult = {
				success: true,
				message: `${scanType} recorded successfully`,
				data: result.data,
				previousScans: prevScanResult.data || []
			};
		} catch (error) {
			console.error('Error recording scan:', error);

			// Store offline
			scanRecord.processed = false;
			scanRecord.status = 'offline'; // Mark as offline
			offlineScans.push(scanRecord);
			localStorage.setItem('offlineScans', JSON.stringify(offlineScans));

			scanResult = {
				success: true,
				message: `${scanType} stored offline (will sync when online)`,
				offline: true,
				previousScans: []
			};
		}
	}

	function getScanType() {
		// Determine scan type based on scanner configuration
		if (scannerConfig.mode === 'check-in') {
			return 'check-in';
		} else if (scannerConfig.mode === 'check-out') {
			return 'check-out';
		} else if (scannerConfig.mode === 'auto') {
			// Auto mode would need to check the last scan for this badge
			// For simplicity, we'll default to check-in for now
			return 'auto';
		} else {
			// Manual mode - would need UI selection
			return 'manual';
		}
	}

	async function manualScanType(type) {
		if (!scanResult || !scanResult.success) return; // Don't process if scan was invalid
		showForceOptions = false;
		const updatedScanRecord = {
			scanner_id: scannerConfig.id,
			qr_data: decodedText,
			scan_type: type
		};

		console.log('Updated scan record:', updatedScanRecord);

		try {
			const response = await fetch('/api/scanner/update-scan', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(updatedScanRecord)
			});

			if (!response.ok) {
				throw new Error('Failed to update scan');
			}

			scanResult.message = `${type} recorded successfully`;

			// Add a short delay to show the success message before resetting
			setTimeout(() => {
				resetScan();
			}, 1500);
		} catch (error) {
			console.error('Error updating scan:', error);
			alert('Failed to update scan type. Please try again.');
		}
	}

	async function syncOfflineScans() {
		if (offlineScans.length === 0) {
			alert('No offline scans to sync');
			return;
		}

		try {
			const response = await fetch('/api/scanner/sync-offline', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ scans: offlineScans })
			});

			if (!response.ok) {
				throw new Error('Failed to sync offline scans');
			}

			// Clear offline scans
			offlineScans = [];
			localStorage.setItem('offlineScans', JSON.stringify(offlineScans));

			alert('Offline scans synced successfully');
		} catch (error) {
			console.error('Error syncing offline scans:', error);
			alert('Failed to sync offline scans. Please try again when online.');
		}
	}

	function resetScan() {
		scanResult = null;
		decodedText = null;
	}

	onDestroy(() => {
		stopScanner();
	});
</script>

<svelte:head>
	<title>QR Scanner</title>
</svelte:head>

<div class="min-h-screen bg-gray-100 flex flex-col">
	<header class="bg-blue-600 text-white p-4 shadow-md">
		<div class="container mx-auto flex justify-between items-center">
			<h1 class="text-xl font-bold">QR Scanner</h1>
			{#if isLoggedIn}
				<div class="flex items-center space-x-2">
					<span>{scannerConfig.name}</span>
					<button
						class="bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm"
						on:click={() => (showSettings = !showSettings)}
					>
						⚙️
					</button>
				</div>
			{/if}
		</div>
	</header>

	<main class="flex-1 container mx-auto p-4">
		{#if !isLoggedIn}
			<div class="bg-white rounded-lg shadow-md p-6 max-w-md mx-auto mt-10">
				<h2 class="text-2xl font-bold mb-6 text-center">Scanner Login</h2>
				<div class="space-y-4">
					<div>
						<label for="username" class="block text-sm font-medium text-gray-700 mb-1"
							>Username</label
						>
						<input
							type="text"
							id="username"
							bind:value={username}
							class="w-full p-2 border rounded"
							placeholder="Enter scanner username"
						/>
					</div>
					<div>
						<label for="password" class="block text-sm font-medium text-gray-700 mb-1"
							>Password</label
						>
						<input
							type="password"
							id="password"
							bind:value={password}
							class="w-full p-2 border rounded"
							placeholder="Enter password"
						/>
					</div>
					<button
						on:click={login}
						class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
					>
						Login
					</button>
				</div>
			</div>
		{:else if showSettings}
			<div class="bg-white rounded-lg shadow-md p-6 max-w-md mx-auto mt-4">
				<h2 class="text-xl font-bold mb-4">Scanner Settings</h2>
				<div class="space-y-4">
					<div>
						<p><strong>Name:</strong> {scannerConfig.name}</p>
						<p><strong>Location:</strong> {scannerConfig.location}</p>
						<p><strong>Gate:</strong> {scannerConfig.gate}</p>
						<p><strong>Mode:</strong> {scannerConfig.mode}</p>
					</div>

					<div class="pt-4 border-t">
						<h3 class="font-medium mb-2">Offline Scans</h3>
						<p>You have {offlineScans.length} offline scans.</p>
						{#if offlineScans.length > 0}
							<button
								on:click={syncOfflineScans}
								class="mt-2 bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
							>
								Sync Offline Scans
							</button>
						{/if}
					</div>

					<div class="pt-4 border-t flex space-x-2">
						<button
							on:click={() => (showSettings = false)}
							class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded"
						>
							Back
						</button>
						<button
							on:click={logout}
							class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
						>
							Logout
						</button>
					</div>
				</div>
			</div>
		{:else if scanResult}
			<div>
				<h2 class="text-xl font-bold mb-4">Scan Result</h2>
				<div class="space-y-4">
					<div class={scanResult.success ? 'text-green-600' : 'text-red-600'}>
						<p class="text-lg font-medium">{scanResult.message}</p>
					</div>

					{#if scanResult.data}
						<div
							class="rounded-lg shadow-md p-6 max-w-md mx-auto mt-4 {scanResult.success
								? 'bg-green-500 border-2 border-green-300'
								: scanResult.validation_failed
									? 'bg-yellow-500 border-2 border-yellow-300'
									: 'bg-red-500 border-2 border-red-300'}"
						>
							<div class="bg-gray-100 p-4 rounded">
								<h3 class="font-medium mb-2">Badge Information</h3>
								<!-- <img
									src={scanResult.data.converted_url}
									alt="Badge Profile"
									class="w-32 h-32 object-cover mb-2"
								/> -->
								<div class="w-32 h-32 mb-2">
									{#await fetchImageBuffer(scanResult.data.converted_url) then imageBuffer}
										{#if imageBuffer}
											<img
												src={URL.createObjectURL(new Blob([imageBuffer]))}
												alt="Badge Profile"
												class="w-full h-full object-cover"
											/>
										{:else}
											<div class="w-full h-full bg-gray-300 flex items-center justify-center">
												<span>No image</span>
											</div>
										{/if}
									{:catch error}
										<div class="w-full h-full bg-gray-300 flex items-center justify-center">
											<span>Failed to load image</span>
										</div>
									{/await}
								</div>
								<p>
									<strong>First Name:</strong>
									{scanResult.data.first_name || 'N/A'}
								</p>
								<p>
									<strong>Last Name:</strong>
									{scanResult.data.last_name || 'N/A'}
								</p>
								<p><strong>Category:</strong> {scanResult.data.category || 'N/A'}</p>
								<p><strong>T-Shirt Size:</strong> {scanResult.data.tshirt_size || 'N/A'}</p>
								<!-- <p><strong>Organization:</strong> {scanResult.data.organization || 'N/A'}</p> -->
							</div>
						</div>
					{/if}
					<!-- #TODO: Conditional rendering for when sure not found -->
					<!-- Only show scan type options if scan was successful -->
					{#if scannerConfig.mode === 'manual' && !scanResult.offline && scanResult.success}
						<div class="pt-4 border-t">
							<h3 class="font-medium text-lg mb-2">Select Scan Type</h3>
							<div class="flex space-x-2 justify-center items-center">
								<button
									on:click={() => manualScanType('check-in')}
									class="bg-green-600 hover:bg-green-700 text-white text-2xl font-bold py-2 px-4 rounded {scanResult.previousScans &&
									scanResult.previousScans.length > 0 &&
									(scanResult.previousScans[0].scan_type === 'check-in' ||
										scanResult.previousScans[0].scan_type === 'force-check-in')
										? 'opacity-50 cursor-not-allowed'
										: ''}"
									disabled={scanResult.previousScans &&
										scanResult.previousScans.length > 0 &&
										(scanResult.previousScans[0].scan_type === 'check-in' ||
											scanResult.previousScans[0].scan_type === 'force-check-in')}
								>
									Check In
								</button>
								<button
									on:click={() => manualScanType('check-out')}
									class="bg-orange-600 hover:bg-orange-700 text-white text-2xl font-bold py-2 px-4 rounded {scanResult.previousScans &&
									scanResult.previousScans.length > 0 &&
									(scanResult.previousScans[0].scan_type === 'check-out' ||
										scanResult.previousScans[0].scan_type === 'force-check-out')
										? 'opacity-50 cursor-not-allowed'
										: ''}"
									disabled={scanResult.previousScans &&
										scanResult.previousScans.length > 0 &&
										(scanResult.previousScans[0].scan_type === 'check-out' ||
											scanResult.previousScans[0].scan_type === 'force-check-out')}
								>
									Check Out
								</button>
								<div class="relative">
									<button
										on:click={() => (showForceOptions = !showForceOptions)}
										class="bg-gray-500 hover:bg-gray-600 text-white text-2xl font-bold py-2 px-3 rounded"
									>
										⋮
									</button>
									{#if showForceOptions}
										<div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
											<div class="py-1">
												<button
													on:click={() => manualScanType('force-check-in')}
													class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
												>
													Force Check In
												</button>
												<button
													on:click={() => manualScanType('force-check-out')}
													class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
												>
													Force Check Out
												</button>
											</div>
										</div>
									{/if}
								</div>
							</div>
						</div>
					{/if}

					<!-- Only show previous scans section if scan was successful -->
					{#if scanResult && scanResult.success && scanResult.previousScans && scanResult.previousScans.length > 0}
						<div class="mt-4 p-4 bg-gray-100 rounded-lg">
							<h3 class="text-lg font-semibold mb-2">Previous Scans</h3>
							<div class="space-y-2">
								{#each scanResult.previousScans as scan, i}
									{#if i === 0}
										<div class="p-2 bg-blue-100 rounded border border-blue-300">
											<p class="font-medium">Last scan: {scan.scan_type} at {scan.gate}</p>
											<p class="text-sm text-gray-600">
												{new Date(scan.timestamp).toLocaleString()}
											</p>
										</div>
									{:else}
										<div class="p-2 bg-gray-50 rounded border border-gray-200">
											<p>{scan.scan_type} at {scan.gate}</p>
											<p class="text-sm text-gray-600">
												{new Date(scan.timestamp).toLocaleString()}
											</p>
										</div>
									{/if}
								{/each}
							</div>
						</div>
					{:else if scanResult && scanResult.success && scanResult.previousScans && scanResult.previousScans.length === 0}
						<div class="mt-4 p-4 bg-gray-100 rounded-lg">
							<p>No previous scans found for this QR code.</p>
						</div>
					{/if}

					<div class="pt-4 flex justify-center">
						<button
							on:click={resetScan}
							class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
						>
							New Scan
						</button>
					</div>
				</div>
			</div>
		{:else}
			<div class="bg-white rounded-lg shadow-md p-6 max-w-md mx-auto mt-4">
				{#if isScanning}
					<div class="mb-4">
						<h2 class="text-xl font-bold mb-2">Scanning...</h2>
						<p class="text-sm text-gray-600 mb-4">Position the QR code within the scanner area</p>
						<div class="relative w-full h-64 bg-black rounded overflow-hidden">
							<QrcodeStream
								formats={['qr_code']}
								{onDetect}
								onError={onScanError}
								paused={!isScanning}
							>
								<div
									class="absolute inset-0 border-2 border-white opacity-50 m-auto w-48 h-48"
								></div>
							</QrcodeStream>
						</div>
					</div>
					<button
						on:click={stopScanner}
						class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
					>
						Cancel
					</button>
				{:else}
					<h2 class="text-xl font-bold mb-4">Ready to Scan</h2>
					<div class="space-y-4">
						<p>Scanner: <strong>{scannerConfig.name}</strong></p>
						<p>Location: <strong>{scannerConfig.location}</strong></p>
						<p>Gate: <strong>{scannerConfig.gate}</strong></p>
						<p>Mode: <strong>{scannerConfig.mode}</strong></p>

						<div class="pt-4 flex space-x-2">
							<button
								on:click={startScanner}
								class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded"
							>
								Start Scanner
							</button>
							<!-- <button
								on:click={() => (showSettings = true)}
								class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded"
							>
								Settings
							</button> -->
						</div>

						{#if offlineScans.length > 0}
							<div class="pt-4 border-t">
								<p class="text-amber-600">
									You have {offlineScans.length} offline scans that need to be synced.
								</p>
								<button
									on:click={syncOfflineScans}
									class="mt-2 bg-amber-600 hover:bg-amber-700 text-white font-bold py-2 px-4 rounded"
								>
									Sync Now
								</button>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		{/if}
	</main>

	<footer class="bg-gray-800 text-white p-4 mt-auto">
		<div class="container mx-auto text-center text-sm">
			<p>QR Scanner App v1.0</p>
		</div>
	</footer>
</div>

<!-- <pre>{JSON.stringify(scanResult, null, 2)}</pre> -->
