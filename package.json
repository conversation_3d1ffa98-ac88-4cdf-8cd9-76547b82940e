{"name": "icc-event", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@types/pdfkit": "^0.13.9", "@types/qrcode": "^1.5.5", "autoprefixer": "^10.4.21", "bits-ui": "^0.22.0", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "lucide-svelte": "^0.501.0", "postcss": "^8.5.3", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.5"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "barcode-detector": "^3.0.1", "bcrypt": "^5.1.1", "buffer": "^6.0.3", "html5-qrcode": "^2.2.7", "jszip": "^3.10.1", "papaparse": "^5.5.2", "pdfkit": "^0.16.0", "qrcode": "^1.5.4", "webrtc-adapter": "^9.0.3", "zeptomail": "^6.2.1"}}