<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import * as Card from '$lib/components/ui/card/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
</script>

<div class="flex h-screen items-center justify-center">
	<Card.Root class="mx-auto max-w-sm">
		<Card.Header>
			<Card.Title class="text-2xl">Login</Card.Title>
			<Card.Description>Enter your email below to login to your account</Card.Description>
		</Card.Header>
		<Card.Content>
			<form method="POST" action="?/login">
				<div class="grid gap-4">
					<div class="grid gap-2">
						<Label for="email">Email</Label>
						<Input id="email" name="email" type="email" placeholder="<EMAIL>" required />
					</div>
					<div class="grid gap-2">
						<div class="flex items-center">
							<Label for="password">Password</Label>
							<!-- <a href="##" class="ml-auto inline-block text-sm underline"> Forgot your password? </a> -->
						</div>
						<Input id="password" name="password" type="password" required />
					</div>
					<Button type="submit" class="w-full">Login</Button>
				</div>
				<div class="mt-4 text-center text-sm">
					Don&apos;t have an account? Contact admin
					<!-- <button class="underline" formaction="?/signup">Sign up</button> -->
				</div>
			</form>
		</Card.Content>
	</Card.Root>
</div>
