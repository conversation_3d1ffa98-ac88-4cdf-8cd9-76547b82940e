<script>
	import { Button } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Select } from '$lib/components/ui/select';
	import { onMount } from 'svelte';

	export let data;

	let newScanner = {
		name: '',
		username: '',
		password: '',
		location: '',
		mode: 'manual', // manual, check-in, check-out, auto
		requiresValidation: false,
		gate: ''
	};

	async function createScanner() {
		try {
			const response = await fetch('/private/api/create-scanner', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(newScanner)
			});

			if (!response.ok) {
				throw new Error('Failed to create scanner');
			}

			// Reset form and reload data
			newScanner = {
				name: '',
				username: '',
				password: '',
				location: '',
				mode: 'manual',
				requiresValidation: false,
				gate: ''
			};

			// Reload the page to refresh data
			window.location.reload();
		} catch (error) {
			console.error('Error creating scanner:', error);
			alert('Failed to create scanner. Please check the console for details.');
		}
	}

	async function deleteScanner(id) {
		if (!confirm('Are you sure you want to delete this scanner?')) return;

		try {
			const response = await fetch(`/private/api/delete-scanner/${id}`, {
				method: 'DELETE'
			});

			if (!response.ok) {
				throw new Error('Failed to delete scanner');
			}

			// Reload the page to refresh data
			window.location.reload();
		} catch (error) {
			console.error('Error deleting scanner:', error);
			alert('Failed to delete scanner. Please check the console for details.');
		}
	}
</script>

<div class="mx-auto p-4">
	<div class="flex flex-col gap-4">
		<div class="flex justify-between items-center">
			<h1 class="text-2xl font-bold">QR Scanner Configuration</h1>
		</div>

		<div class="bg-white p-4 rounded-lg border shadow-sm">
			<h2 class="text-xl font-semibold mb-4">Create New Scanner</h2>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label for="name">Scanner Name</Label>
					<Input id="name" bind:value={newScanner.name} placeholder="e.g. Gate A Scanner" />
				</div>
				<div>
					<Label for="username">Username</Label>
					<Input id="username" bind:value={newScanner.username} placeholder="username" />
				</div>
				<div>
					<Label for="password">Password</Label>
					<Input
						id="password"
						type="password"
						bind:value={newScanner.password}
						placeholder="Password"
					/>
				</div>
				<div>
					<Label for="location">Location</Label>
					<Input id="location" bind:value={newScanner.location} placeholder="e.g. Doha Stadium" />
				</div>
				<div>
					<Label for="gate">Gate/Section</Label>
					<Input id="gate" bind:value={newScanner.gate} placeholder="e.g. Zone 1" />
				</div>
				<div>
					<Label for="mode">Scanning Mode</Label>
					<select id="mode" bind:value={newScanner.mode} class="w-full p-2 border rounded">
						<option value="manual">Manual (Staff selects check-in/out)</option>
						<option value="check-in">Check-in Only</option>
						<option value="check-out">Check-out Only</option>
						<option value="auto">Auto (Toggles between check-in/out)</option>
					</select>
				</div>
				<div class="flex items-center space-x-2 mt-4">
					<input
						type="checkbox"
						id="requiresValidation"
						bind:checked={newScanner.requiresValidation}
						class="h-4 w-4"
					/>
					<Label for="requiresValidation">Requires Validation</Label>
				</div>
			</div>
			<Button onclick={createScanner} class="mt-4">Create Scanner</Button>
		</div>

		<div class="rounded-lg border">
			<h2 class="text-xl font-semibold p-4">Existing Scanners</h2>
			<Table.Root>
				<Table.Header>
					<Table.Row>
						<Table.Head>Name</Table.Head>
						<Table.Head>Username</Table.Head>
						<Table.Head>Password</Table.Head>
						<Table.Head>Location</Table.Head>
						<Table.Head>Gate/Section</Table.Head>
						<Table.Head>Mode</Table.Head>
						<Table.Head>Requires Validation</Table.Head>
						<Table.Head>Actions</Table.Head>
					</Table.Row>
				</Table.Header>
				<Table.Body>
					{#each data.scanners as scanner}
						<Table.Row>
							<Table.Cell>{scanner.name}</Table.Cell>
							<Table.Cell>{scanner.username}</Table.Cell>
							<Table.Cell>{scanner.password_hash}</Table.Cell>
							<Table.Cell>{scanner.location}</Table.Cell>
							<Table.Cell>{scanner.gate}</Table.Cell>
							<Table.Cell>{scanner.mode}</Table.Cell>
							<Table.Cell>{scanner.requires_validation ? 'Yes' : 'No'}</Table.Cell>
							<Table.Cell>
								<Button variant="destructive" size="sm" onclick={() => deleteScanner(scanner.id)}>
									Delete
								</Button>
							</Table.Cell>
						</Table.Row>
					{/each}
				</Table.Body>
			</Table.Root>
		</div>
	</div>
</div>
