import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const DELETE: RequestHandler = async ({ params, locals: { supabase } }) => {
    try {
        const { id } = params;

        console.log('Deleting guest with ID:', id);

        const { error } = await supabase
            .from('workforce')
            .delete()
            .eq('id', id);

        if (error) {
            console.error('Error deleting guest:', error);
            return json({ success: false, error: error.message }, { status: 500 });
        }

        return json({ success: true });
    } catch (error) {
        console.error('Error in delete-guest endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};