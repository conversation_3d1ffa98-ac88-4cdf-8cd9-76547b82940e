<script lang="ts">
	import { onMount } from 'svelte';
	import <PERSON> from 'papaparse';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import * as Card from '$lib/components/ui/card/index.js';
	import * as Table from '$lib/components/ui/table/index.js';

	let fileInput: HTMLInputElement;
	let fileName = '';
	let csvData: Record<string, string>[] = [];
	let rowCount = 0;

	let processingResults: any = null;
	let result: any = null;

	// Variables to store response data
	let isLoading = true;
	let error = null;
	let responseData = null;

	let row: any = null;

	// function readFile() {
	// 	const file = fileInput?.files?.[0];
	// 	if (file) {
	// 		fileName = file.name; // Set file name immediately
	// 		Papa.parse(file, {
	// 			complete: (results) => {
	// 				csvData = results.data.filter((row) => Object.keys(row).length > 0); // Remove empty rows
	// 				rowCount = csvData.length;
	// 			},
	// 			header: true,
	// 			delimiter: ',',
	// 			transformHeader: (header) => header.trim().toLowerCase().replace(/\s+/g, '')
	// 		});
	// 	}
	// }

	// Added white space trimming
	// function readFile() {
	// 	const file = fileInput?.files?.[0];
	// 	if (file) {
	// 		fileName = file.name;
	// 		Papa.parse(file, {
	// 			complete: (results) => {
	// 				// Trim whitespace from all values in each row
	// 				csvData = results.data
	// 					.filter((row) => Object.keys(row).length > 0)
	// 					.map((row) => {
	// 						const trimmedRow = {};
	// 						Object.entries(row).forEach(([key, value]) => {
	// 							trimmedRow[key] = typeof value === 'string' ? value.trim() : value;
	// 						});
	// 						return trimmedRow;
	// 					});
	// 				rowCount = csvData.length;
	// 			},
	// 			header: true,
	// 			delimiter: ',',
	// 			transformHeader: (header) => header.trim().toLowerCase().replace(/\s+/g, '')
	// 		});
	// 	}
	// }

	// Added remove white space and empty rows
	function readFile() {
		const file = fileInput?.files?.[0];
		if (file) {
			fileName = file.name;
			Papa.parse(file, {
				complete: (results) => {
					// Filter out empty rows and trim values
					csvData = results.data
						.filter((row) => {
							// Check if row has any non-empty values
							return Object.values(row).some(
								(value) => value !== null && value !== undefined && String(value).trim() !== ''
							);
						})
						.map((row) => {
							const trimmedRow = {};
							Object.entries(row).forEach(([key, value]) => {
								trimmedRow[key] = typeof value === 'string' ? value.trim() : value;
							});
							return trimmedRow;
						});
					rowCount = csvData.length;
				},
				header: true,
				delimiter: ',',
				transformHeader: (header) => header.trim().toLowerCase().replace(/\s+/g, '')
			});
		}
	}

	async function handleAddToDatabase() {
		const response = await fetch('?/addToDatabase', {
			// its a server action
			method: 'POST',
			body: JSON.stringify(csvData)
		});

		result = await response.json();
		// console.log('Result from addToDB: Result:', result);

		if (result.status === 200) {
			// Access the results directly from result.data
			// processingResults = result.data.results;
			// console.log('Processing Results:', processingResults);
			alert('Data added to the database successfully!');
		} else {
			alert(`Error: ${result.data?.message || 'Unknown error occurred'}`);
		}
	}

	onMount(() => {
		fileInput = document.getElementById('csvFile') as HTMLInputElement;
	});
</script>

<Card.Root class="mx-auto w-full">
	<Card.Header>
		<Card.Title>CSV File Processor and Database Updater</Card.Title>
	</Card.Header>
	<Card.Content class="space-y-6">
		<div class="flex items-center gap-4">
			<div class="flex-1">
				<input type="file" id="csvFile" accept=".csv" class="sr-only" onchange={readFile} />
				<label
					for="csvFile"
					class="inline-flex h-9 cursor-pointer items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
				>
					Choose File {fileName ? `: ${fileName}` : ''}
				</label>
			</div>
		</div>

		<div class="flex gap-2">
			<Button onclick={handleAddToDatabase}>Add to Database</Button>
		</div>

		{#if error}
			<p class="text-red-500">{error}</p>
		{/if}

		{#if csvData.length > 0}
			<div class="space-y-2">
				<div class="text-sm text-muted-foreground">
					CSV Content
					<br />
					Total rows: {rowCount}
				</div>
				<div class="rounded-lg border">
					<Table.Root>
						<Table.Header>
							<Table.Row>
								{#each Object.keys(csvData[0]) as header}
									<Table.Head>{header}</Table.Head>
								{/each}
							</Table.Row>
						</Table.Header>
						<Table.Body>
							{#each csvData as row}
								<Table.Row>
									{#each Object.keys(csvData[0]) as key}
										<Table.Cell>{row[key]}</Table.Cell>
									{/each}
								</Table.Row>
							{/each}
						</Table.Body>
					</Table.Root>
				</div>
			</div>
		{/if}
	</Card.Content>
</Card.Root>
