import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { sendEmailToGuest } from '$lib/server/sendEmail';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const { guest, eventDetails } = await request.json();

        // Validate input
        if (!guest || !eventDetails) {
            return json({ success: false, error: 'Missing guest or event details' }, { status: 400 });
        }

        // Send email using the server function
        const result = await sendEmailToGuest(guest, eventDetails);

        if (result.success) {
            // Update email status in database
            try {
                const { error: updateError } = await supabase
                    .from('workforce')
                    .update({
                        email_status: 'sent',
                        email_sent_at: new Date().toISOString()
                    })
                    .eq('id', guest.id);

                if (updateError) {
                    console.error('Error updating email status:', updateError);
                    // Don't fail the request if status update fails
                }
            } catch (dbError) {
                console.error('Database update error:', dbError);
                // Don't fail the request if status update fails
            }

            return json({ 
                success: true, 
                message: `<PERSON><PERSON> sent successfully to ${guest.first_name} ${guest.last_name}`,
                data: result 
            });
        } else {
            return json({ 
                success: false, 
                error: result.error || 'Failed to send email',
                guestEmail: result.guestEmail 
            }, { status: 500 });
        }
    } catch (error) {
        console.error('Error in send-single-email API:', error);
        return json({ 
            success: false, 
            error: 'Internal server error' 
        }, { status: 500 });
    }
};
