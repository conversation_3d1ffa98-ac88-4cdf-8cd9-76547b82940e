import { error } from '@sveltejs/kit';
import PDFDocument from 'pdfkit';
import JSZ<PERSON> from 'jszip';
import { pdfBuffer } from '$lib/utils/pdfBuffer';
import { join } from 'path';
import QRCode from 'qrcode';
import { combineZones } from '$lib/utils/combineZones.js';
import { combineFunctionalAreas } from '$lib/utils/combineFunctionalArea.js';

export async function POST({ request }) {
    let successCount = 0;
    let failedCount = 0;
    let totalBadges = 0;
    console.log('bulk badge generation code running..');
    try {
        const badgesData = await request.json();
        // console.log('Received badges data:', badgesData);
        totalBadges = badgesData.length;
        console.info('Total badges to generate:', totalBadges);

        // need to set a start time
        // const startTime = Date.now();


        const zip = new JSZip();

        // Generate PDF for each badge
        for (const [index, data] of badgesData.entries()) {
            // console.log('Processing badge data:', data);

            const doc = new PDFDocument({
                size: [595.28, 419.53],
                margin: 0,
                info: {
                    Title: 'QiA Champions League 2025 Badge',
                    Author: 'eHaris Badge Generator'
                }
            });

            try {
                // Use the existing PDF generation logic wrapped in pdfBuffer
                const pdfData = await pdfBuffer(doc, async (doc) => {
                    // Register fonts first
                    doc.registerFont('Changa-Regular', join(process.cwd(), 'src', 'lib', 'fonts', 'Changa-Regular.ttf'));
                    doc.registerFont('Changa-SemiBold', join(process.cwd(), 'src', 'lib', 'fonts', 'Changa-SemiBold.ttf'));

                    // Background image
                    // doc.image(join(process.cwd(), 'src', 'lib', 'images', 'guest.jpg'), 0, 0, {
                    //     width: doc.page.width,
                    //     height: doc.page.height
                    // });

                    // console.log("Data Category for loading images:", data.category)


                    // Loading image logic
                    switch (data.category) {
                        case 'GUEST':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'guest.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;
                        case 'MEDIA':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'media.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;
                        case 'MEDICAL':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'medical.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;
                        case 'ORGANISER':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'org.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;
                        case 'SECURITY':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'sec.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;
                        case 'TEAM OFFICIAL':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'team.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;
                        case 'PLAYER':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'player.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;
                        case 'VOLUNTEER':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'vol.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;

                        case 'LEADER':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'leader.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;

                        case 'SERVICES':
                            doc.image(join(process.cwd(), 'src', 'lib', 'images', 'services.jpg'), 0, 0, {
                                width: doc.page.width,
                                height: doc.page.height
                            });
                            break;

                    }

                    // Define colors
                    const maroonColor = '#69081c';


                    // Profile Photo
                    if (data.imageUrl) {
                        try {
                            // Fetch the image
                            const imageResponse = await fetch(data.imageUrl);
                            if (!imageResponse.ok) {
                                throw new Error('Failed to load image');
                            }

                            const imageArrayBuffer = await imageResponse.arrayBuffer();
                            const imageBuffer = Buffer.from(imageArrayBuffer);

                            // Add the image to the PDF 
                            // x (left/right),y (top/bottom),h,w
                            doc.image(imageBuffer, 17, 123, {
                                fit: [95, 95],
                                align: 'center'
                            });
                        } catch (imageError) {
                            console.error('Error loading image:', imageError);
                            // Draw empty photo box if image fails
                            // doc.rect(80, 80, 12, 130)
                            //     .strokeColor('#999999')
                            //     .stroke();

                            // doc.fontSize(10)
                            //     .fillColor('#333333')
                            //     .text('Photo not available', 45, 280);
                        }
                    } else {
                        console.log('Photo Else block hit')
                        // Draw empty photo box
                        // doc.rect(80, 80, 120, 140)
                        //     .strokeColor('#999999')
                        //     .stroke();
                    }

                    // Add First name - with explicit error handling
                    try {
                        doc.font('Changa-SemiBold')
                            .fontSize(20) // previous size 28
                            .fillColor(maroonColor)
                            .text(data.firstName, 118, 112);
                        // console.log('Added firstName:', data.firstName);
                    } catch (error) {
                        console.error('Error adding firstName:', error);
                    }

                    // Add Last name
                    try {
                        doc.font('Changa-Regular')
                            .fontSize(18) // previous size 24
                            .fillColor(maroonColor)
                            .text(data.lastName, 118, 135);
                        // console.log('Added lastName:', data.lastName);
                    } catch (error) {
                        console.error('Error adding lastName:', error);
                    }

                    // Add designation
                    try {
                        doc.font('Changa-Regular')
                            .fontSize(12) // previous size 18
                            .fillColor(maroonColor)
                            .text(data.designation || '', 118, 195);
                        // console.log('Added designation:', data.designation);
                    } catch (error) {
                        console.error('Error adding designation:', error);
                    }
                    // Add designation 2
                    try {
                        doc.font('Changa-Regular')
                            .fontSize(12) // previous size 18
                            .fillColor(maroonColor)
                            .text(data.designation2 || '', 118, 205);
                        // console.log('Added designation:', data.designation2);
                    } catch (error) {
                        console.error('Error adding designation:', error);
                    }


                    // QR code
                    try {
                        if (data.qrData) {
                            // Generate QR code as a data URL
                            const qrCodeDataURL = await QRCode.toDataURL(data.qrData, {
                                width: 100,
                                margin: 0
                            });

                            // Convert data URL to buffer
                            const qrImageData = qrCodeDataURL.split(',')[1];
                            const qrBuffer = Buffer.from(qrImageData, 'base64');

                            // Add QR code   // x (left/right),y (top/bottom),h,w
                            // doc.image(qrBuffer, 236, 50, {
                            doc.image(qrBuffer, 229, 48, {
                                fit: [62, 62]
                                // fit: [50, 50]
                            });
                        }
                    } catch (qrError) {
                        console.error('Error generating QR code:', qrError);
                        doc.rect(50, 50, 240, 130)
                            .strokeColor('#999999')
                            .stroke();
                    }

                    // Add QR as text ID
                    doc.font('Helvetica')
                        .fontSize(5)
                        .fillColor(maroonColor)
                        .text(data.qrData || 'ID#', 248, 113);


                    // set functional areas
                    // console.log('Data:', data);
                    let combinedFunctionalAreas = combineFunctionalAreas(data);
                    // console.log('Combined Functional Areas:', combinedFunctionalAreas);

                    // Determine selected Functional areas
                    // const selectedAreas = Object.entries(data.functionalAreas)
                    const selectedAreas = Object.entries(combinedFunctionalAreas)
                        .filter(([_, selected]) => selected)
                        .map(([area]) => area);

                    // console.log('Selected Areas: ', selectedAreas);


                    // Set up the Functional Areas Grid
                    if (selectedAreas.length > 0) {
                        // Grid configuration
                        const maxColumns = 4;
                        const maxRows = 3;
                        const startX = 19;
                        const startY = 240;
                        const rectWidth = 52;
                        const rectHeight = 23; // was 23
                        const horizontalSpacing = 15;
                        const verticalSpacing = 5;

                        selectedAreas.forEach((area, index) => {
                            // Calculate row and column position
                            const column = index % maxColumns;
                            const row = Math.floor(index / maxColumns);

                            // Skip if exceeding max rows
                            if (row >= maxRows) return;

                            // Calculate x and y positions
                            const xPosition = startX + (column * (rectWidth + horizontalSpacing));
                            const yPosition = startY + (row * (rectHeight + verticalSpacing));


                            // Create rectangle
                            doc.rect(xPosition, yPosition, rectWidth, rectHeight)
                                .fill(maroonColor);

                            // Add centered text
                            doc.font('Helvetica-Bold')
                                .fontSize(18)
                                .fillColor('white')
                                .text(area, xPosition, yPosition + 5, {
                                    width: rectWidth,
                                    align: 'center'
                                });
                        });
                    }

                    // Zone

                    // console.log('Data:', data);
                    let combinedZones = combineZones(data);
                    // console.log('Combined Zones:', combinedZones);


                    // Determine selected zones
                    const selectedZones = Object.entries(combinedZones)
                        // const selectedZones = Object.entries(data.zones)
                        .filter(([_, selected]) => selected)
                        .map(([zone]) => zone);

                    // console.log('Selected Zones: ', selectedZones);

                    if (selectedZones.length > 0) {
                        // Grid configuration for zones
                        const maxColumns = 3;
                        const maxRows = 2;
                        const startX = 20;
                        const startY = 350; // Positioned below the functional areas
                        const rectWidth = 60;
                        const rectHeight = 20;

                        const horizontalSpacing = 40;
                        const verticalSpacing = 8;


                        selectedZones.forEach((zone, index) => {
                            // Calculate row and column position
                            const column = index % maxColumns;
                            const row = Math.floor(index / maxColumns);

                            // Skip if exceeding max rows
                            if (row >= maxRows) return;

                            // Calculate x and y positions
                            const xPosition = startX + (column * (rectWidth + horizontalSpacing));
                            const yPosition = startY + (row * (rectHeight + verticalSpacing));




                            // Add centered text
                            doc.font('Helvetica-Bold')
                                .fontSize(30)
                                .fillColor('white')
                                .text(zone, xPosition, yPosition + 5, {
                                    width: rectWidth,
                                    align: 'center'
                                });
                        });
                    }
                    // Add eHaris Branding
                    // doc.font('Helvetica')
                    //     .fontSize(8)
                    //     .fillColor('white')
                    //     .text('Accreditation ID Secured by eHaris Qatar', 380, 409);

                });

                // Add PDF to zip with a unique name
                // const filename = `badge_${data.badgeId || index}.pdf`; // has issues with dupliate file name, it will get overwritten
                const filename = `badge_${data.badgeId || index}_${Date.now()}.pdf`;
                zip.file(filename, pdfData);
                console.log('Added PDF to zip:', filename);
                successCount++;
                console.log('Success count:', successCount + '/' + totalBadges);

            } catch (error) {
                failedCount++;
                console.error('Error generating PDF for badge:', data.badgeId, error);
                console.error('Fail count:', failedCount + '/' + totalBadges);
            }
        }

        // Generate zip file
        const zipBuffer = await zip.generateAsync({ type: 'uint8array' });

        console.log('Zip file generated.. ready to download');

        return new Response(zipBuffer, {
            headers: {
                'Content-Type': 'application/zip',
                'Content-Disposition': 'attachment; filename="badges.zip"'
            }
        });
    } catch (e) {
        console.error('Error generating bulk PDFs:', e);
        throw error(500, 'Failed to generate PDFs');
    }
}

