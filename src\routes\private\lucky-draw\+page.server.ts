import type { PageServerLoad } from './$types';
import { supabaseAdminTickets } from '$lib/server/db/supabaseAdminTickets';
import { error } from '@sveltejs/kit';

export const load: PageServerLoad = async () => {
    const today = new Date().toISOString().split('T')[0];
    // console.log('today', today);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    // console.log('tomorrow', tomorrowStr);

    // Get the Latest event with the slug = game
    const { data: todaysMatch, error: todaysMatchError } = await supabaseAdminTickets
        .from('events')
        .select('id, title, title_slug')
        .eq('title_slug', 'game')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

    if (todaysMatchError) {
        console.error('Error fetching guests:', todaysMatchError);
        throw error(500, 'Failed to fetch guests');
    }

    console.log('todaysMatch', todaysMatch);
    const eventId = todaysMatch?.id;
    console.log('Event ID: ', eventId)

    // Get today's guests
    const { data: todayGuests, error: guestsError } = await supabaseAdminTickets
        .from('guests')
        .select('id, first_name, last_name, mobile, created_at')
        .eq('event_id', eventId)


    if (guestsError) {
        console.error('Error fetching guests:', guestsError);
        throw error(500, 'Failed to fetch guests');
    }

    // console.log('todayGuests', todayGuests);

    console.log('Todays Guest Count: ', todayGuests?.length)

    // Get today's winners
    const { data: winners, error: winnersError } = await supabaseAdminTickets
        .from('lucky_draw_winners')
        .select('guest_id, position')
        .gte('created_at', today)
        .lt('created_at', tomorrowStr);

    if (winnersError) {
        console.error('Error fetching winners:', winnersError);
        throw error(500, 'Failed to fetch winners');
    }

    // console.log('winners', winners);
    console.log('winners Count', winners?.length);

    console.log('Eligible Guests', todayGuests?.length - winners?.length);

    return {
        todayGuests: todayGuests || [],
        winners: winners || []
    };
};

export const actions = {
    drawWinners: async () => {

        console.log('======================= Drawing Winners =====================');
        const today = new Date().toISOString().split('T')[0];

        // Get the Latest event with the slug = game
        const { data: todaysMatch, error: todaysMatchError } = await supabaseAdminTickets
            .from('events')
            .select('id, title, title_slug')
            .eq('title_slug', 'game')
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

        if (todaysMatchError) {
            console.error('Error fetching guests:', todaysMatchError);
            throw error(500, 'Failed to fetch guests');
        }

        console.log('todaysMatch', todaysMatch);
        const eventId = todaysMatch?.id;
        console.log('Event ID: ', eventId)

        // Get today's guests
        const { data: todayGuests, error: guestsError } = await supabaseAdminTickets
            .from('guests')
            .select('id, first_name, last_name, mobile, created_at')
            .eq('event_id', eventId)


        if (guestsError) {
            console.error('Error fetching guests:', guestsError);
            throw error(500, 'Failed to fetch guests');
        }

        // console.log('todayGuests', todayGuests);
        console.log('Todays Guest Count: ', todayGuests?.length)

        // Get today's winners
        const { data: existingWinners, error: winnersError } = await supabaseAdminTickets
            .from('lucky_draw_winners')
            .select('guest_id, position')
            .gte('created_at', today);

        if (winnersError) {
            console.error('Error fetching winners:', winnersError);
            return { success: false, error: 'Failed to fetch existing winners' };
        }

        console.log('existingWinners Count: ', existingWinners?.length);

        // Filter out guests who have already won
        const existingWinnerIds = existingWinners?.map(w => w.guest_id) || [];
        const eligibleGuests = todayGuests?.filter(g => !existingWinnerIds.includes(g.id)) || [];
        console.log('======================= Filter out guests who have already won =====================');
        console.log('eligibleGuests', eligibleGuests);
        console.log('eligibleGuests.length', eligibleGuests.length);
        console.log('existingWinnerIds', existingWinnerIds);
        console.log('existingWinners', existingWinners);

        if (eligibleGuests.length < 1) {
            return {
                success: false,
                error: `No eligible guests remaining for drawing`
            };
        }

        // Randomly select 1 winner
        const shuffled = [...eligibleGuests].sort(() => 0.5 - Math.random());
        const newWinner = shuffled[0];

        // Get the next position number - check for existing positions to avoid constraint violation
        const existingPositions = existingWinners?.map(w => w.position) || [];
        let nextPosition = 1;
        console.log('existingPositions', existingPositions);
        console.log('nextPosition', nextPosition);

        // Find the next available position (1, 2, 3, etc.)
        while (existingPositions.includes(nextPosition)) {
            nextPosition++;
        }

        // Insert winner into database
        const winnerToInsert = {
            guest_id: newWinner.id,
            position: nextPosition,
            created_at: new Date().toISOString()
        };

        const { error: insertError } = await supabaseAdminTickets
            .from('lucky_draw_winners')
            .insert([winnerToInsert]);

        if (insertError) {
            console.error('Error inserting winner:', insertError);
            return { success: false, error: 'Failed to save winner' };
        }

        return {
            success: true,
            winner: {
                ...newWinner,
                position: nextPosition
            }
        };
    }
};

