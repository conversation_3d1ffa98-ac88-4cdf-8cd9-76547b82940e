<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import * as Select from '$lib/components/ui/select';
	import { goto } from '$app/navigation';

	// Form state using Svelte 5 runes
	let formData = $state({
		firstName: '',
		lastName: '',
		email: '',
		mobile: '',
		whatsapp: '',
		organization: '',
		tshirtSize: '',
		category: ''
	});

	let isSubmitting = $state(false);
	let errors = $state<Record<string, string>>({});
	let successMessage = $state('');

	// Category options as specified in requirements
	const categoryOptions = [
		'YOGA TRAINER',
		'ACTIVATION',
		'EMBASSY OFFICIAL',
		'VOLUNTEER',
		'ORGANISER',
		'MEDIA',
		'MEDICAL',
		'GUEST',
		'PARTICIPANT'
	];

	// T-shirt size options
	const tshirtSizeOptions = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'];

	// Form validation
	function validateForm(): boolean {
		const newErrors: Record<string, string> = {};

		if (!formData.firstName.trim()) {
			newErrors.firstName = 'First name is required';
		}

		if (!formData.email.trim()) {
			newErrors.email = 'Email is required';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			newErrors.email = 'Please enter a valid email address';
		}

		if (!formData.mobile.trim()) {
			newErrors.mobile = 'Mobile number is required';
		} else if (!/^\+?[\d\s\-\(\)]+$/.test(formData.mobile)) {
			newErrors.mobile = 'Please enter a valid mobile number';
		}

		if (!formData.category) {
			newErrors.category = 'Category is required';
		}

		if (!formData.tshirtSize) {
			newErrors.tshirtSize = 'T-shirt size is required';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	}

	// Handle form submission
	async function handleSubmit() {
		if (!validateForm()) {
			return;
		}

		isSubmitting = true;
		successMessage = '';

		try {
			const response = await fetch('/private/api/add-user', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(formData)
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to add user');
			}

			successMessage = 'User added successfully!';
			
			// Reset form
			formData = {
				firstName: '',
				lastName: '',
				email: '',
				mobile: '',
				whatsapp: '',
				organization: '',
				tshirtSize: '',
				category: ''
			};
			errors = {};

			// Optionally redirect after a delay
			setTimeout(() => {
				goto('/private/all');
			}, 2000);

		} catch (error) {
			console.error('Error adding user:', error);
			errors.submit = error instanceof Error ? error.message : 'Failed to add user';
		} finally {
			isSubmitting = false;
		}
	}

	// Handle category selection
	function handleCategorySelect(selected: any) {
		if (selected?.value) {
			formData.category = selected.value;
			if (errors.category) {
				delete errors.category;
				errors = { ...errors };
			}
		}
	}

	// Handle t-shirt size selection
	function handleTshirtSizeSelect(selected: any) {
		if (selected?.value) {
			formData.tshirtSize = selected.value;
			if (errors.tshirtSize) {
				delete errors.tshirtSize;
				errors = { ...errors };
			}
		}
	}
</script>

<div class="container mx-auto p-4 max-w-2xl">
	<div class="flex justify-between items-center mb-6">
		<h1 class="text-3xl font-bold">Add New User</h1>
		<Button href="/private" variant="outline">Back to Home</Button>
	</div>

	{#if successMessage}
		<div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
			{successMessage}
		</div>
	{/if}

	{#if errors.submit}
		<div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
			{errors.submit}
		</div>
	{/if}

	<form on:submit|preventDefault={handleSubmit} class="space-y-6">
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			<!-- First Name -->
			<div class="space-y-2">
				<Label for="firstName">First Name *</Label>
				<Input
					id="firstName"
					type="text"
					bind:value={formData.firstName}
					class={errors.firstName ? 'border-red-500' : ''}
					placeholder="Enter first name"
					required
				/>
				{#if errors.firstName}
					<p class="text-sm text-red-600">{errors.firstName}</p>
				{/if}
			</div>

			<!-- Last Name -->
			<div class="space-y-2">
				<Label for="lastName">Last Name</Label>
				<Input
					id="lastName"
					type="text"
					bind:value={formData.lastName}
					placeholder="Enter last name"
				/>
			</div>

			<!-- Email -->
			<div class="space-y-2">
				<Label for="email">Email *</Label>
				<Input
					id="email"
					type="email"
					bind:value={formData.email}
					class={errors.email ? 'border-red-500' : ''}
					placeholder="Enter email address"
					required
				/>
				{#if errors.email}
					<p class="text-sm text-red-600">{errors.email}</p>
				{/if}
			</div>

			<!-- Mobile -->
			<div class="space-y-2">
				<Label for="mobile">Mobile *</Label>
				<Input
					id="mobile"
					type="tel"
					bind:value={formData.mobile}
					class={errors.mobile ? 'border-red-500' : ''}
					placeholder="Enter mobile number"
					required
				/>
				{#if errors.mobile}
					<p class="text-sm text-red-600">{errors.mobile}</p>
				{/if}
			</div>

			<!-- WhatsApp -->
			<div class="space-y-2">
				<Label for="whatsapp">WhatsApp</Label>
				<Input
					id="whatsapp"
					type="tel"
					bind:value={formData.whatsapp}
					placeholder="Enter WhatsApp number"
				/>
			</div>

			<!-- Organization -->
			<div class="space-y-2">
				<Label for="organization">Organization</Label>
				<Input
					id="organization"
					type="text"
					bind:value={formData.organization}
					placeholder="Enter organization name"
				/>
			</div>

			<!-- Category -->
			<div class="space-y-2">
				<Label for="category">Category *</Label>
				<Select.Root onSelectedChange={handleCategorySelect}>
					<Select.Trigger class={errors.category ? 'border-red-500' : ''}>
						<Select.Value placeholder="Select category" />
					</Select.Trigger>
					<Select.Content>
						{#each categoryOptions as category}
							<Select.Item value={category}>{category}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
				{#if errors.category}
					<p class="text-sm text-red-600">{errors.category}</p>
				{/if}
			</div>

			<!-- T-shirt Size -->
			<div class="space-y-2">
				<Label for="tshirtSize">T-shirt Size *</Label>
				<Select.Root onSelectedChange={handleTshirtSizeSelect}>
					<Select.Trigger class={errors.tshirtSize ? 'border-red-500' : ''}>
						<Select.Value placeholder="Select size" />
					</Select.Trigger>
					<Select.Content>
						{#each tshirtSizeOptions as size}
							<Select.Item value={size}>{size}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
				{#if errors.tshirtSize}
					<p class="text-sm text-red-600">{errors.tshirtSize}</p>
				{/if}
			</div>
		</div>

		<!-- Submit Button -->
		<div class="flex justify-end space-x-4">
			<Button type="button" variant="outline" onclick={() => goto('/private')}>
				Cancel
			</Button>
			<Button type="submit" disabled={isSubmitting}>
				{isSubmitting ? 'Adding User...' : 'Add User'}
			</Button>
		</div>
	</form>
</div>
