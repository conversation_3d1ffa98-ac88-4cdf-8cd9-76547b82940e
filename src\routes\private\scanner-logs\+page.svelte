<script lang="ts">
	import type { PageData } from './$types';
	import * as Table from '$lib/components/ui/table';
	import * as Tabs from '$lib/components/ui/tabs';
	import { Button } from '$lib/components/ui/button';
	import { onMount } from 'svelte';

	let { data }: { data: PageData } = $props();
	let validScans: any[] = data.scans;
	let invalidScans: any[] = data.invalidScans;
	let currentDate = new Date().toISOString().split('T')[0];

	// onMount(() => {
	// 	filterScansByDate();
	// });

	// function filterScansByDate() {
	// 	// Filter valid scans for current day
	// 	validScans = data.scans.filter((scan) => {
	// 		return scan.timestamp.startsWith(currentDate);
	// 	});

	// 	// Filter invalid scans for current day
	// 	invalidScans = data.invalidScans.filter((scan) => {
	// 		return scan.timestamp.startsWith(currentDate);
	// 	});
	// }

	function exportToCSV(scans, filename) {
		// Create CSV header
		const headers = Object.keys(scans[0] || {}).join(',');

		// Create CSV rows
		const csvRows = scans.map((scan) =>
			Object.values(scan)
				.map((value) => (typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value))
				.join(',')
		);

		// Combine header and rows
		const csvContent = [headers, ...csvRows].join('\n');

		// Create download link
		const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
		const url = URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.setAttribute('href', url);
		link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.csv`);
		link.style.visibility = 'hidden';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	}

	function exportAllScans() {
		exportToCSV(data.scans, 'valid-scans');
	}

	function exportAllInvalidScans() {
		exportToCSV(data.invalidScans, 'invalid-scans');
	}
</script>

<div class="container mx-auto py-8">
	<div class="flex justify-between items-center mb-6">
		<h1 class="text-2xl font-bold">Scanner Logs</h1>
		<div class="space-x-2">
			<Button on:click={exportAllScans}>Export All Valid Scans</Button>
			<Button on:click={exportAllInvalidScans}>Export All Invalid Scans</Button>
		</div>
	</div>

	<Tabs.Root value="valid" class="w-full">
		<Tabs.List class="grid grid-cols-2 w-full mb-4">
			<Tabs.Trigger value="valid">Valid Scans</Tabs.Trigger>
			<Tabs.Trigger value="invalid">Invalid Scans</Tabs.Trigger>
		</Tabs.List>

		<Tabs.Content value="valid">
			<div class="rounded-lg border">
				<h2 class="text-xl font-semibold p-4">Valid Scans (Today)</h2>
				<Table.Root>
					<Table.Header>
						<Table.Row>
							<Table.Head>ID</Table.Head>
							<Table.Head>QR Data</Table.Head>
							<Table.Head>Timestamp</Table.Head>
							<Table.Head>Scanner</Table.Head>
							<Table.Head>Gate</Table.Head>
							<Table.Head>Scan Type</Table.Head>
							<Table.Head>Status</Table.Head>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						{#each validScans as scan}
							<Table.Row>
								<Table.Cell>{scan.id}</Table.Cell>
								<Table.Cell>{scan.qr_data}</Table.Cell>
								<Table.Cell>{new Date(scan.timestamp).toLocaleString()}</Table.Cell>
								<Table.Cell>{scan.scanner_name}</Table.Cell>
								<Table.Cell>{scan.gate}</Table.Cell>
								<Table.Cell>{scan.scan_type}</Table.Cell>
								<Table.Cell>{scan.status}</Table.Cell>
							</Table.Row>
						{:else}
							<Table.Row>
								<Table.Cell colspan="7" class="text-center py-4"
									>No valid scans for today</Table.Cell
								>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			</div>
		</Tabs.Content>

		<Tabs.Content value="invalid">
			<div class="rounded-lg border">
				<h2 class="text-xl font-semibold p-4">Invalid Scans (Today)</h2>
				<Table.Root>
					<Table.Header>
						<Table.Row>
							<Table.Head>ID</Table.Head>
							<Table.Head>QR Data</Table.Head>
							<Table.Head>Timestamp</Table.Head>
							<Table.Head>Scanner</Table.Head>
							<Table.Head>Gate</Table.Head>
							<Table.Head>Scan Type</Table.Head>
							<Table.Head>Status</Table.Head>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						{#each invalidScans as scan}
							<Table.Row>
								<Table.Cell>{scan.id}</Table.Cell>
								<Table.Cell>{scan.qr_data}</Table.Cell>
								<Table.Cell>{new Date(scan.timestamp).toLocaleString()}</Table.Cell>
								<Table.Cell>{scan.scanner_name}</Table.Cell>
								<Table.Cell>{scan.gate}</Table.Cell>
								<Table.Cell>{scan.scan_type}</Table.Cell>
								<Table.Cell>{scan.status}</Table.Cell>
							</Table.Row>
						{:else}
							<Table.Row>
								<Table.Cell colspan="7" class="text-center py-4"
									>No invalid scans for today</Table.Cell
								>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			</div>
		</Tabs.Content>
	</Tabs.Root>
</div>
