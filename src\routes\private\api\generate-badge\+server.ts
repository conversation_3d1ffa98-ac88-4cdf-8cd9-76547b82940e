// src/routes/api/generate-badge/+server.js
import { error } from '@sveltejs/kit';
import PDFDocument from 'pdfkit';
import QRCode from 'qrcode';
import { Buffer } from 'buffer';
import { join } from 'path';
import { pdfBuffer } from '$lib/utils/pdfBuffer';
import { combineZones } from '$lib/utils/combineZones.js';
import { combineFunctionalAreas } from '$lib/utils/combineFunctionalArea.js';

export async function POST({ request }) {
    try {
        const data = await request.json();

        console.log('Data in Generate Badge API: ', data);

        // Create a PDF document
        const doc = new PDFDocument({
            size: [595.28, 419.53], // A5 landscape in points
            margin: 0,
            info: {
                Title: 'QiA Champions League 2025 Badge',
                Author: 'eHaris Badge Generator'
            }
        });

        // Use the pdfBuffer utility to handle PDF generation
        const pdfData = await pdfBuffer(doc, async (doc) => {
            // Register fonts
            doc.registerFont('Changa-Regular', join(process.cwd(), 'src', 'lib', 'fonts', 'Changa-Regular.ttf'));
            doc.registerFont('Changa-SemiBold', join(process.cwd(), 'src', 'lib', 'fonts', 'Changa-SemiBold.ttf'));

            // Background image based on category
            switch (data.category) {
                case 'GUEST':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'guest.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;
                case 'MEDIA':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'media.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;
                case 'MEDICAL':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'medical.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;
                case 'ORGANISER':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'org.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;
                case 'SECURITY':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'sec.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;
                case 'TEAM OFFICIAL':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'team.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;
                case 'PLAYER':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'player.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;
                case 'VOLUNTEER':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'vol.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;
                case 'LEADER':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'leader.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;

                case 'SERVICES':
                    doc.image(join(process.cwd(), 'src', 'lib', 'images', 'services.jpg'), 0, 0, {
                        width: doc.page.width,
                        height: doc.page.height
                    });
                    break;

            }

            // Define colors
            const maroonColor = '#69081c';

            // Photo area
            if (data.imageUrl) {
                try {
                    const imageResponse = await fetch(data.imageUrl);
                    if (!imageResponse.ok) throw new Error('Failed to load image');

                    const imageArrayBuffer = await imageResponse.arrayBuffer();
                    const imageBuffer = Buffer.from(imageArrayBuffer);

                    doc.image(imageBuffer, 17, 123, {
                        fit: [95, 95],
                        align: 'center'
                    });
                } catch (imageError) {
                    console.error('Error loading image:', imageError);
                }
            }



            // Add First name

            doc.font('Changa-SemiBold')
                .fontSize(20)
                .fillColor(maroonColor)
                .text(data.firstName || '', 118, 112);
            console.log('First Name: ', data.firstName);


            // Add Last name 

            doc.font('Changa-Regular')
                .fontSize(18)
                .fillColor(maroonColor)
                .text(data.lastName || '', 118, 135);
            console.log('Designation: ', data.designation);



            // Add designation
            doc.font('Changa-Regular')
                .fontSize(12)
                .fillColor(maroonColor)
                .text(data.designation || '', 118, 195);
            console.log('Designation: ', data.designation);

            doc.font('Changa-Regular')
                .fontSize(12) // previous size 18
                .fillColor(maroonColor)
                .text(data.designation2 || '', 118, 205);
            console.log('Designation 2:', data.designation2);

            // QR code
            if (data.qrData) {
                try {
                    const qrCodeDataURL = await QRCode.toDataURL(data.qrData, {
                        width: 100,
                        margin: 0
                    });

                    const qrImageData = qrCodeDataURL.split(',')[1];
                    const qrBuffer = Buffer.from(qrImageData, 'base64');

                    doc.image(qrBuffer, 229, 48, {
                        fit: [62, 62]
                    });

                    // Add QR as text ID
                    doc.font('Helvetica')
                        .fontSize(5)
                        .fillColor(maroonColor)
                        .text(data.qrData, 248, 113);
                } catch (qrError) {
                    console.error('Error generating QR code:', qrError);
                }
            }
            console.log('QR Data: ', data.qrData);


            // Logic Since the Data is coming form the Form, we are using the data from the Form to parse it. 
            // While we use the combined Zones to parse the data from the DB.


            let combinedFunctionalAreas = combineFunctionalAreas(data);
            console.log('Combined Functional Areas:', combinedFunctionalAreas);


            // Determine selected Functional areas
            const selectedAreas = Object.entries(combinedFunctionalAreas)
                // const selectedAreas = Object.entries(data.functionalAreas)
                .filter(([_, selected]) => selected)
                .map(([area]) => area);




            // Set up the Functional Areas Grid
            if (selectedAreas.length > 0) {
                // Grid configuration
                const maxColumns = 4;
                const maxRows = 3;
                const startX = 19;
                const startY = 240;
                const rectWidth = 52;
                const rectHeight = 23;
                const horizontalSpacing = 15;
                const verticalSpacing = 5;

                selectedAreas.forEach((area, index) => {
                    // Calculate row and column position
                    const column = index % maxColumns;
                    const row = Math.floor(index / maxColumns);

                    // Skip if exceeding max rows
                    if (row >= maxRows) return;

                    // Calculate x and y positions
                    const xPosition = startX + (column * (rectWidth + horizontalSpacing));
                    const yPosition = startY + (row * (rectHeight + verticalSpacing));


                    // Create rectangle
                    doc.rect(xPosition, yPosition, rectWidth, rectHeight)
                        .fill(maroonColor);

                    // Add centered text
                    doc.font('Helvetica-Bold')
                        .fontSize(18)
                        .fillColor('white')
                        .text(area, xPosition, yPosition + 5, {
                            width: rectWidth,
                            align: 'center'
                        });
                });
            }


            // Zone
            let combinedZones = combineZones(data);

            // Logic Since the Data is coming form the Form, we are using the data from the Form to parse it. 
            // While we use the combined Zones to parse the data from the DB.


            // Determine selected zones
            // const selectedZones = Object.entries(data.zones)
            const selectedZones = Object.entries(combinedZones)
                .filter(([_, selected]) => selected)
                .map(([zone]) => zone);

            // console.log('Selected Zones: ', selectedZones);

            if (selectedZones.length > 0) {
                // Grid configuration for zones
                const maxColumns = 3;
                const maxRows = 2;
                const startX = 20; // Start 20
                const startY = 350; // Positioned below the functional areas start 344
                const rectWidth = 60; // was 60
                const rectHeight = 20; // was 30

                const horizontalSpacing = 40; // was 40
                const verticalSpacing = 8; // was 2


                selectedZones.forEach((zone, index) => {
                    // Calculate row and column position
                    const column = index % maxColumns;
                    const row = Math.floor(index / maxColumns);

                    // Skip if exceeding max rows
                    if (row >= maxRows) return;

                    // Calculate x and y positions
                    const xPosition = startX + (column * (rectWidth + horizontalSpacing));
                    const yPosition = startY + (row * (rectHeight + verticalSpacing));

                    // Add centered text
                    doc.font('Helvetica-Bold')
                        .fontSize(30) // was 32
                        .fillColor('white')
                        .text(zone, xPosition, yPosition + 5, {
                            width: rectWidth,
                            align: 'center'
                        });
                });
            }

            console.log('END')
        });

        // Create filename
        const filename = `${data.badgeId || 'badge'}_${data.firstName || 'firstname'}_${data.lastName || 'lastname'}_${data.category || 'category'}.pdf`;

        // Return the PDF buffer as a response
        return new Response(pdfData, {
            headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': `inline; filename="${filename}"`
            }
        });

    } catch (e) {
        console.error('Error generating PDF:', e);
        throw error(500, 'Failed to generate PDF');
    }
}
