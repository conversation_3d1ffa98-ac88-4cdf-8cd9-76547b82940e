import { addToDB } from '$lib/server/addToDB';
import type { Actions } from '@sveltejs/kit';
import { fail, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { safeGetSession } }) => {
    const { session } = await safeGetSession();
    if (!session) {
        redirect(303, '/');
    }

};

export const actions: Actions = {
    addToDatabase: async ({ request }) => {
        const data = await request.json();

        if (!data || !Array.isArray(data)) {
            return fail(400, {
                status: false,
                data: { message: 'Invalid data format' }
            });
        }

        if (data.length === 0) {
            return fail(400, {
                status: false,
                data: { message: 'No data provided' }
            });
        }

        const result = await addToDB(data);





        // console.log('Result from addToDB: Result.message:', result.message);

        console.log('Result from addToDB sever function: Result:', result);
        // console.log('Result from addToDB: Result.rows:', result.total);

        // Return the data directly without additional wrapping
        return {
            // totalRows: result.total,
            // successCount: result.successCount,
            // failedCount: result.failedCount
            ...result
        };
    }
};
