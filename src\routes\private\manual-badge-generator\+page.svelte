<!-- src/routes/badge-generator/+page.svelte -->
<script>
	let formData = {
		imageUrl: '',
		badgeId: '',
		// name: '<PERSON><PERSON>',
		firstName: '',
		lastName: '',
		designation: '',
		designation2: '',
		category: '',
		qrData: '',
		functionalAreas: {
			ACC: false, // Accreditation & Access Management
			COM: false, // Competitions
			CRL: false, // Corporate Relations
			EVA: false, // Events & Activations
			FNP: false, // Finance & Purchases
			FBS: false, // Food & Beverages
			GMH: false, // Guest Management & Hospitality
			LOG: false, // Logistics
			MME: false, // Marketing & Media
			OGC: false, // Organising Committee
			PRG: false, // PR & Govt Relations
			TML: false, // Team Liaison
			WKF: false // Workforce
		},
		zones: {
			1: false, // Field of Play
			2: false, // Dressing Room
			3: false, // Media Tribune
			4: false, // VVIP Area
			5: false, // Family Area
			6: false // Public Area
		}
	};

	async function generatePDF() {
		try {
			const response = await fetch('/private/api/add-data-manual', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(formData)
			});

			console.log('RESPONSE: ', response);
		} catch (error) {
			console.error('Error adding data to DB:', error);
		}

		try {
			const response = await fetch('/private/api/generate-badge-manual', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(formData)
			});

			if (!response.ok) {
				throw new Error('Failed to generate PDF');
			}

			// Convert the response to a blob
			const blob = await response.blob();

			// Create a URL for the blob
			const url = URL.createObjectURL(blob);

			// Create a temporary link element
			const link = document.createElement('a');
			link.href = url;
			// link.download = 'badge.pdf'; // Set the download filename
			link.download = `${formData.badgeId}_${formData.firstName || 'firstname'}_${formData.lastName || 'lastname'}_${formData.category || 'category'}.pdf`; // Set the download filename

			// Open the PDF in a new tab
			window.open(url, '_blank');

			// Trigger download
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			// Clean up by revoking the object URL after a delay
			setTimeout(() => URL.revokeObjectURL(url), 1000);
		} catch (error) {
			console.error('Error generating PDF:', error);
			alert('Failed to generate PDF. Please check the console for more details.');
		}
	}
</script>

<div class="container mx-auto p-4 max-w-4xl">
	<h1 class="text-2xl font-bold mb-6">QiA Champions League 2025 Badge Generator</h1>

	<form onsubmit={generatePDF} class="space-y-6">
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div>
				<label for="imageUrl" class="block mb-1 font-medium">Profile Picture URL:</label>
				<input
					id="imageUrl"
					type="url"
					bind:value={formData.imageUrl}
					class="w-full p-2 border rounded"
					placeholder="https://example.com/image.jpg"
					required
				/>
			</div>

			<!-- <div>
				<label for="name" class="block mb-1 font-medium">Name:</label>
				<input
					id="name"
					type="text"
					bind:value={formData.name}
					class="w-full p-2 border rounded"
					placeholder="e.g. Afreen Fathima"
					required
				/>
			</div> -->

			<div>
				<label for="badgeid" class="block mb-1 font-medium">Badge ID:</label>
				<input
					id="badgeid"
					type="text"
					bind:value={formData.badgeId}
					class="w-full p-2 border rounded"
					placeholder="e.g. VID123456"
					required
				/>
			</div>
			<div>
				<label for="fname" class="block mb-1 font-medium">Fist Name:</label>
				<input
					id="fname"
					type="text"
					bind:value={formData.firstName}
					class="w-full p-2 border rounded"
					placeholder="e.g. Afreen Fathima"
					required
				/>
			</div>

			<div>
				<label for="lname" class="block mb-1 font-medium">Last Name:</label>
				<input
					id="lname"
					type="text"
					bind:value={formData.lastName}
					class="w-full p-2 border rounded"
					placeholder="e.g. Afreen Fathima"
					required
				/>
			</div>

			<!-- <div>
				<label for="designation" class="block mb-1 font-medium">Designation:</label>
				<input
					id="designation"
					type="text"
					bind:value={formData.designation}
					class="w-full p-2 border rounded"
					required
				/>
				<p class="text-sm text-gray-500 mt-1">Default: Volunteer</p>
			</div> -->

			<div>
				<label for="designation" class="block mb-1 font-medium">Designation:</label>
				<input
					id="designation"
					type="text"
					bind:value={formData.designation}
					class="w-full p-2 border rounded"
					placeholder="e.g. MEDIA FA"
				/>
			</div>

			<div>
				<label for="designation2" class="block mb-1 font-medium">Designation 2:</label>
				<input
					id="designation2"
					type="text"
					bind:value={formData.designation2}
					class="w-full p-2 border rounded"
					placeholder="e.g. MEDIA FA"
				/>
			</div>

			<div>
				<label for="category" class="block mb-1 font-medium">Category:</label>
				<select
					id="category"
					bind:value={formData.category}
					class="w-full p-2 border rounded"
					required
				>
					<option value="GUEST">GUEST</option>
					<option value="MEDIA">MEDIA</option>
					<option value="MEDICAL">MEDICAL</option>
					<option value="ORGANISER">ORGANISER</option>
					<option value="PLAYER">PLAYER</option>
					<option value="SECURITY">SECURITY</option>
					<option value="SERVICES">SERVICES</option>
					<option value="LEADER">TEAM LEADER</option>
					<option value="TEAM OFFICIAL">TEAM OFFICIAL</option>
					<option value="VOLUNTEER">VOLUNTEER</option>
				</select>
				<!-- <p class="text-sm text-gray-500 mt-1">Default: Volunteer</p> -->
			</div>

			<div>
				<label for="qrData" class="block mb-1 font-medium">QR Code Data:</label>
				<input
					id="qrData"
					type="text"
					bind:value={formData.qrData}
					class="w-full p-2 border rounded"
					placeholder="e.g. VID123456"
					required
				/>
			</div>
		</div>

		<div>
			<h2 class="font-bold text-lg mb-2">Functional Areas:</h2>
			<div class="grid grid-cols-1 md:grid-cols-3 gap-2">
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.ACC} class="mr-2" />
					<span class="font-semibold mr-1">ACC</span> - Accreditation & Access Management
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.COM} class="mr-2" />
					<span class="font-semibold mr-1">COM</span> - Competitions
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.CRL} class="mr-2" />
					<span class="font-semibold mr-1">CRL</span> - Corporate Relations
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.EVA} class="mr-2" />
					<span class="font-semibold mr-1">EVA</span> - Events & Activations
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.FNP} class="mr-2" />
					<span class="font-semibold mr-1">FNP</span> - Finance & Purchases
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.FBS} class="mr-2" />
					<span class="font-semibold mr-1">FBS</span> - Food & Beverages
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.GMH} class="mr-2" />
					<span class="font-semibold mr-1">GMH</span> - Guest Management & Hospitality
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.LOG} class="mr-2" />
					<span class="font-semibold mr-1">LOG</span> - Logistics
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.MME} class="mr-2" />
					<span class="font-semibold mr-1">MME</span> - Marketing & Media
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.OGC} class="mr-2" />
					<span class="font-semibold mr-1">OGC</span> - Organising Committee
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.PRG} class="mr-2" />
					<span class="font-semibold mr-1">PRG</span> - PR & Govt Relations
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.TML} class="mr-2" />
					<span class="font-semibold mr-1">TML</span> - Team Liaison
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.functionalAreas.WKF} class="mr-2" />
					<span class="font-semibold mr-1">WKF</span> - Workforce
				</label>
			</div>
		</div>

		<div>
			<h2 class="font-bold text-lg mb-2">Zones:</h2>
			<div class="grid grid-cols-2 md:grid-cols-3 gap-2">
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.zones[1]} class="mr-2" />
					<span class="font-semibold mr-1">1</span> - Field of Play
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.zones[2]} class="mr-2" />
					<span class="font-semibold mr-1">2</span> - Dressing Room
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.zones[3]} class="mr-2" />
					<span class="font-semibold mr-1">3</span> - Media Tribune
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.zones[4]} class="mr-2" />
					<span class="font-semibold mr-1">4</span> - VVIP Area
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.zones[5]} class="mr-2" />
					<span class="font-semibold mr-1">5</span> - Family Area
				</label>
				<label class="flex items-center p-2 border rounded hover:bg-gray-50">
					<input type="checkbox" bind:checked={formData.zones[6]} class="mr-2" />
					<span class="font-semibold mr-1">6</span> - Public Area
				</label>
			</div>
			<!-- <p class="text-sm text-gray-500 mt-2">
				Note: Up to 2 zones will be displayed prominently on the badge.
			</p> -->
		</div>

		<button
			type="submit"
			class="bg-red-800 text-white py-3 px-8 rounded-md hover:bg-red-900 font-bold text-lg"
		>
			Generate Badge PDF
		</button>
	</form>
</div>
