<script>
	import Button from '$lib/components/ui/button/button.svelte';

	let { data, children } = $props();
	let { supabase } = $derived(data);

	const logout = async () => {
		const { error } = await supabase.auth.signOut();
		if (error) {
			console.error(error);
		}
	};
</script>

<header class="flex justify-between items-center p-4">
	<nav>
		<Button href="/private">Home</Button>
		<!-- <a href="/private">Home</a> -->
	</nav>
	<Button
		class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
		onclick={logout}>Logout</Button
	>
	<!-- <button onclick={logout}>Logout</button> -->
</header>
<main>
	{@render children()}
</main>
