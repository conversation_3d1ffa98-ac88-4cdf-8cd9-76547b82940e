<script>
	import But<PERSON> from '$lib/components/ui/button/button.svelte';
</script>

<div class="min-h-screen flex flex-col items-center justify-center p-4">
	<h1 class="text-4xl font-bold mb-8">Badge Generator</h1>

	<!-- <div class="grid grid-cols-2 gap-4 md:grid-cols-4 max-w-4xl">
		<button
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
			><a href="/guest"> GUEST</a>
		</button>
		<button
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
		>
			ORGANISER
		</button>
		<button
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
		>
			SECURITY
		</button>
		<button
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
		>
			MEDICAL
		</button>
		<button
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
		>
			MEDIA
		</button>
		<button
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
		>
			TEAM OFFICIAL & PLAYER
		</button>
		<button
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
		>
			VOLUNTEER
		</button>
		<button
			class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
		>
			ORGANISER
		</button>
	</div> -->

	<div>
		<Button href="/scanner">Scanner</Button>
	</div>
</div>

<p class="text-sm text-center">Build: 07-May-2025 @1107hrs</p>

<style lang="postcss">
	:global(html) {
		background-color: theme(colors.gray.100);
	}
</style>
