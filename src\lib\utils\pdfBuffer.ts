// src/lib/utils/pdfBuffer.ts
export function pdfBuffer(doc, designFn) {
    return new Promise<Buffer>(async (resolve, reject) => {
        try {
            const buffers: Uint8Array[] = [];

            // Wait for the design function to complete
            await designFn(doc);

            doc.on('data', chunk => buffers.push(chunk));
            doc.on('end', () => resolve(Buffer.concat(buffers)));
            doc.on('error', reject);

            // End the document after all operations are complete
            doc.end();
        } catch (error) {
            reject(error);
        }
    });
}
