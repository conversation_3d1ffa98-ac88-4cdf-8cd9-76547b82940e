import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const scanRecord = await request.json();

        // Get scanner info to check if validation is required
        const { data: scanner, error: scannerError } = await supabase
            .from('scanners')
            .select('requires_validation, gate')
            .eq('id', scanRecord.scanner_id)
            .single();

        if (scannerError) {
            console.error('Error fetching scanner info:', scannerError);
            return json({ success: false, error: 'Scanner not found' }, { status: 404 });
        }

        // Try to fetch badge information based on the QR data
        const { data: badgeData, error: badgeError } = await supabase
            .from('workforce')
            .select('first_name, last_name, category, tshirt_size, converted_url')
            .eq('qr_data', scanRecord.qr_data)
            .single();

        // Validation logic
        if (scanner.requires_validation) {
            // Check if user exists in the database
            if (badgeError || !badgeData) {
                return json({
                    success: false,
                    error: 'QR Code not found in database',
                    validation_failed: true
                }, { status: 403 });
            }
        }

        // Insert the scan record into the database
        const { data, error } = await supabase
            .from('scans')
            .insert({
                qr_data: scanRecord.qr_data,
                timestamp: scanRecord.timestamp,
                scanner_id: scanRecord.scanner_id,
                scanner_name: scanRecord.scanner_name,
                gate: scanRecord.gate,
                scan_type: scanRecord.scan_type
            })
            .select()
            .single();

        if (error) {
            console.error('Error recording scan:', error);
            return json({ success: false, error: error.message }, { status: 500 });
        }

        return json({
            success: true,
            data: badgeData || null,
            scan_id: data.id
        });
    } catch (error) {
        console.error('Error in record-scan endpoint:', error);
        return json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
};


