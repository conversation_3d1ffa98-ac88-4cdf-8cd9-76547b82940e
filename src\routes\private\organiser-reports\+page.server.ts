import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals: { supabase } }) => {
    // First get all volunteer QR codes and their details
    const { data: volunteers } = await supabase
        .from('workforce')
        .select('qr_data, first_name, last_name, badge_id')
        .eq('category', 'ORGANISER');

    // Extract QR codes from volunteers
    const volunteerQrCodes = volunteers?.map(v => v.qr_data) || [];

    // console.log('volunteer QR codes: ', volunteerQrCodes);

    // Create a lookup map for volunteer details
    const volunteerDetails = volunteers?.reduce((acc, volunteer) => {
        acc[volunteer.qr_data] = {
            first_name: volunteer.first_name,
            last_name: volunteer.last_name,
            badge_id: volunteer.badge_id
        };
        return acc;
    }, {} as Record<string, { first_name: string | null, last_name: string | null, badge_id: string | null }>);

    // console.log('volunteer details: ', volunteerDetails);

    // Then fetch scans that match these QR codes
    const { data: scanData, error } = await supabase
        .from('scans')
        .select('*')
        .in('qr_data', volunteerQrCodes)
        .order('timestamp', { ascending: false });

    if (error) {
        console.error('Error fetching volunteer scans:', error);
    }

    // Combine scan data with volunteer details
    const volunteerScans = scanData?.map(scan => {
        const details = volunteerDetails?.[scan.qr_data] || {
            first_name: null,
            last_name: null,
            badge_id: null
        };

        return {
            ...scan,
            first_name: details.first_name,
            last_name: details.last_name,
            badge_id: details.badge_id
        };
    }) || [];

    return {
        volunteerScans
    };
};
